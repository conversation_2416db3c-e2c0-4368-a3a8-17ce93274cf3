<?php

return [
    //1
   "processes" => "Процессы",
    "clients" => "Клиенты",
    "client_section" => "Раздел клиентов",
    "salesSection" => "Продажи",
    "finance" => "Финансы",
    "pays" => "Платежи",
    "expenses" => "Расходы",
    "expense_type" => "Тип расходов",
    "supplier" => "Поставщик",
    "material_return" => "Возврат материалов",
    "no_pending_returns" => "Нет ожидающих подтверждения возвратов",
    "confirm_action" => "Подтверждение действия",
    "accept" => "Подтвердить",
    "total_sum" => "Общая сумма",
    "materials" => "Материалы",
    "storage" => "Склад",
    "product" => "Продукт",
    "material" => "Тип сырья",
    "equipment" => "Техника",
    "equipment_parts" => "Запчасти техники",
    "back_to_equipment" => "Назад к технике",
    "add_part" => "Добавить запчасть",
    "filter_by_status" => "Фильтр по статусу",
    "search_by_name" => "Поиск по названию",
    "enter_part_name" => "Введите название запчасти",
    "not_installed" => "Не установлена",
    "no_parts_for_equipment" => "Для данной техники запчасти не найдены",
    "edit_part" => "Редактировать запчасть",
    "send_to_defect" => "Отправить в брак",
    "Error occurred while searching" => "Произошла ошибка при поиске",
    "Error loading photo" => "Ошибка загрузки фото",
    "select_part" => "Выберите запчасть",
    "available" => "Доступно",
    "pcs" => "шт",
    "max_available" => "Максимум доступно",
    "optional_comment" => "Дополнительный комментарий (необязательно)",
    "part_info" => "Информация о запчасти",
    "available_quantity" => "Доступное количество",
    "currency" => "сум",
    "not_specified" => "Не указано",
    "Error occurred while adding part" => "Произошла ошибка при добавлении запчасти",
    "Invalid part or quantity" => "Неверная запчасть или количество",
    "Part not found" => "Запчасть не найдена",
    "Insufficient quantity available" => "Недостаточное количество в наличии",
    "Part moved to equipment" => "Запчасть перемещена к оборудованию",
    "Part created from reserve and installed" => "Запчасть создана из резерва и установлена",
    "Quantity reduced: {quantity} units moved to equipment" => "Количество уменьшено: {quantity} единиц перемещено к оборудованию",
    "Part successfully added to equipment" => "Запчасть успешно добавлена к оборудованию",
    "Part fully moved to equipment: {quantity} units" => "Запчасть полностью перемещена к оборудованию: {quantity} единиц",
    "Part created from reserve and installed: {quantity} units" => "Запчасть создана из резерва и установлена: {quantity} единиц",
    "Quantity reduced from {old} to {new}: {moved} units moved to equipment {equipment}" => "Количество уменьшено с {old} до {new}: {moved} единиц перемещено к оборудованию {equipment}",
    "Error updating source part quantity" => "Ошибка обновления количества исходной запчасти",
    "Error creating new part record" => "Ошибка создания новой записи запчасти",
    "defect" => "Бракованное сырье",
    "unit_type" => "Единица измерения",
    "unit_piece" => "Штука",
    "unit_kg" => "Килограмм",
    "unit_liter" => "Литр",
    "unknown" => "Неизвестно",
    "is_processed" => "Обработанный материал",
    "product_repackaging" => "Переупаковка продукта",
    "repackaging_reason" => "Причина переупаковки",
    "settings" => "Настройки",
    "users" => "Пользователи",
    "region" => "Регионы",

    // Оборудование и запчасти
    "equipment_parts" => "Запчасти оборудования",
    "part_information" => "Информация о запчасти",
    "current_status" => "Текущий статус",
    "current_equipment" => "Текущее оборудование",
    "history" => "История",
    "part_history" => "История запчасти",
    "part_history_on_equipment" => "История запчасти {part} на оборудовании {equipment}",
    "back_to_parts" => "Назад к запчастям",
    "not_assigned" => "Не назначена",
    "no_history_records_found" => "Записи истории не найдены",

    // Equipment status change with parts handling
    "parts_handling_instructions" => "Инструкции по обработке запчастей",
    "selected_parts_returned_to_warehouse" => "Выбранные запчасти будут возвращены на склад",
    "unselected_parts_sent_to_defect" => "Невыбранные запчасти будут отправлены в брак",
    "select_parts_to_return_to_warehouse" => "Выберите запчасти для возврата на склад",
    "select_all" => "Выбрать все",
    "deselect_all" => "Снять выделение",
    "parts_decommission_warning" => "Внимание: невыбранные запчасти будут безвозвратно списаны в брак!",
    "no_parts_attached_to_equipment" => "К данному оборудованию не прикреплены запчасти",
    "equipment_status_changed_successfully" => "Статус оборудования успешно изменен",
    "returned_to_warehouse_from_decommissioned_equipment" => "Возвращено на склад с списанного оборудования: {equipment}",
    "defected_with_decommissioned_equipment" => "Списано в брак вместе с оборудованием: {equipment}",
    "in_repair" => "в ремонте",
    "date" => "Дата",
    "action" => "Действие",
    "status_before" => "Статус до",
    "status_after" => "Статус после",
    "comment" => "Комментарий",
    "user" => "Пользователь",
    "active" => "Активный",
    "inactive" => "Неактивный",
    "reserve" => "В резерве",
    "purchased" => "Приобретенный",
    "installed" => "Установлен",
    "removed" => "Снят",
    "moved_to_reserve" => "Перемещен в резерв",
    "part_created_and_installed_to_equipment" => "Запчасть создана и установлена на оборудование",
    "part_created_and_added_to_reserve" => "Запчасть создана и добавлена в резерв",
    "equipment_part_created_successfully" => "Запчасть оборудования успешно создана",
    "equipment_part_not_found" => "Запчасть оборудования не найдена",
    "equipment_part_updated_successfully" => "Запчасть оборудования успешно обновлена",
    "part_installed_to_equipment" => "Запчасть установлена на оборудование",
    "part_removed_from_equipment" => "Запчасть снята с оборудования",
    "part_status_changed" => "Статус запчасти изменен",
    "equipment_part_attached_successfully" => "Запчасть успешно прикреплена к оборудованию",
    "equipment_part_is_not_attached_to_any_equipment" => "Запчасть не прикреплена ни к какому оборудованию",
    "equipment_part_detached_successfully" => "Запчасть успешно отсоединена от оборудования",
    "part_detached_from_equipment" => "Запчасть отсоединена от оборудования",
    "status_changed_successfully" => "Статус успешно изменен",
    "error_changing_status" => "Ошибка при изменении статуса",
    "photo_not_found" => "Фото не найдено",
    "error_loading_photo" => "Ошибка при загрузке фото",
    "no_results_found" => "Результаты не найдены",
    "error_occurred_while_searching" => "Произошла ошибка при поиске",
    "select_status" => "Выберите статус",
    "select_source_type" => "Выберите тип источника",
    "select_equipment" => "Выберите оборудование",
    "search" => "Поиск",
    "create_part" => "Создать запчасть",
    "add_item" => "Добавить элемент",
    "archive" => "Архивировать",
    "share" => "Поделиться",
    "archive_functionality_will_be_implemented_in_the_future" => "Функциональность архивирования будет реализована в будущем",
    "share_functionality_will_be_implemented_in_the_future" => "Функциональность совместного использования будет реализована в будущем",
    "parts" => "Запчасти",
    "add_part" => "Добавить запчасть",
    "no_parts_attached_to_this_equipment" => "К этому оборудованию не прикреплены запчасти",
    "not_attached" => "Не прикреплен",
    "photo" => "Фото",
    "name" => "Название",
    "description" => "Описание",
    "price" => "Цена",
    "source_type" => "Тип источника",
    "status" => "Статус",
    "installation_date" => "Дата установки",
    "actions" => "Действия",
    "detail" => "Детали",
    "edit" => "Редактировать",
    "change_status" => "Изменить статус",
    "attach_to_equipment" => "Прикрепить к оборудованию",
    "detach_from_equipment" => "Отсоединить от оборудования",
    "photo_is_required" => "Фото обязательно",
    "move_to_reserve" => "Переместить в резерв",
    "decommission" => "Вывести из эксплуатации",
    "you_are_about_to_decommission_this_equipment" => "Вы собираетесь вывести это оборудование из эксплуатации. Это действие нельзя отменить.",
    "parts_attached_to_this_equipment" => "Запчасти, прикрепленные к этому оборудованию",
    "no_parts_are_attached_to_this_equipment" => "К этому оборудованию не прикреплены запчасти",
    "decommission_with_equipment" => "Вывести из эксплуатации вместе с оборудованием",
    "equipment_decommissioned_successfully" => "Оборудование успешно выведено из эксплуатации",
    "error_decommissioning_equipment" => "Ошибка при выводе оборудования из эксплуатации",
    "error_decommissioning_part" => "Ошибка при выводе запчасти из эксплуатации",
    "error_moving_part_to_reserve" => "Ошибка при перемещении запчасти в резерв",
    "part_decommissioned_with_equipment" => "Запчасть выведена из эксплуатации вместе с оборудованием",
    "part_moved_to_reserve_when_equipment_decommissioned" => "Запчасть перемещена в резерв при выводе оборудования из эксплуатации",
    "reserve_quantity" => "Количество для резерва",
    "max_available_for_reserve" => "Максимум доступно для резерва: {max}",
    "reserve_quantity_exceeds_assigned" => "Количество для резерва превышает назначенное: {available}",
    "Part returned to warehouse from equipment: {equipment} (quantity: {quantity})" => "Запчасть возвращена на склад с оборудования: {equipment} (количество: {quantity})",
    "Part returned to warehouse successfully" => "Запчасть успешно возвращена на склад",
    "create_equipment" => "Создать оборудование",
    "user" => "Пользователь",
    "user_full_name" => "ФИО пользователя",
    "user_role" => "Роль пользователя",
    "role" => "Роль",
    "workers" => "Сотрудники",
    "position" => "Должность",
    "position_section" => "Раздел должностей",
    "position_name" => "Название должности",
    "add_position" => "Добавить должность",
    "edit_position" => "Редактировать должность",
    "position_delete" => "Удалить должность",
    "created_at" => "Дата добавления",
    "status" => "Статус",
    "actions" => "Действия",
    "detail" => "Подробнее",
    "edit" => "Редактировать",
    "delete" => "Удалить",
    "active" => "Активный",
    "inactive" => "Неактивный",
    "save" => "Сохранить",
    "cancel" => "Отмена",
    "add_user" => "Добавить пользователя",
    "edit_user" => "Редактировать пользователя",
    "delete_user" => "Удалить пользователя",
    "create_user" => "Создать пользователя",
    "update_user" => "Обновить пользователя",
    "username" => "Логин",
    "full_name" => "Полное имя",
    "phone_number" => "Номер телефона",
    "phone_number_2" => "Дополнительный номер",
    "password" => "Пароль",
    "leave_empty_if_not_changing" => "Оставьте пустым, если не хотите менять",
    "user_created_successfully" => "Пользователь успешно создан",
    "user_updated_successfully" => "Пользователь успешно обновлен",
    "user_not_found" => "Пользователь не найден",
    "confirm_delete" => "Вы действительно хотите удалить?",

    //2
    'select_role' => 'Выберите роль',
    'search:' => 'Поиск:',
    'Show _MENU_ entries' => 'Показать _MENU_ записей',
    'Nothing found' => 'Ничего не найдено',
    'Showing _PAGE_ to _PAGES_ of _MAX_ items' => 'Показано с _PAGE_ по _PAGES_ из _MAX_ записей',
    '(filtered from _MAX_ records)' => '(отфильтровано из _MAX_ записей)',
    "are_you_sure_delete_user" => "Вы действительно хотите удалить?",
    'region_section' => 'Раздел регионов',
    'region_name' => 'Название региона',
    'add_region' => 'Добавить регион',
    'edit_region' => 'Редактировать регион',
    'region_delete' => 'Удалить регион',
    'Region created successfully.' => 'Регион успешно создан.',
    'Region updated successfully.' => 'Регион успешно обновлен.',
    'Region deleted successfully.' => 'Регион успешно удален.',
    'Error deleting region' => 'Ошибка при удалении региона',
    'Region not found.' => 'Регион не найден.',
    "no_data_available" => "Данные отсутствуют",
    'worker_section' => 'Раздел сотрудников',
    'worker_name' => 'Имя сотрудника',
    'add_worker' => 'Добавить сотрудника',
    'edit_worker' => 'Редактировать сотрудника',
    'worker_delete' => 'Удалить сотрудника',
    'Worker created successfully.' => 'Сотрудник успешно создан.',
    'Worker updated successfully.' => 'Сотрудник успешно обновлен.',
    'Worker deleted successfully.' => 'Сотрудник успешно удален.',
    'Error deleting worker' => 'Ошибка при удалении сотрудника',
    'Worker not found.' => 'Сотрудник не найден.',
    'salary' => 'Зарплата',
    'address' => 'Адрес',
    'select_position' => 'Выберите должность',
    'advance' => 'Аванс',
    'type' => 'Тип',
    'amount' => 'Сумма',
    'month' => 'Месяц',
    'description' => 'Описание',
    'worker_finance_section' => 'Финансовый раздел сотрудников',
    'remaining_salary' => "Оставшаяся зарплата",
    "amount_paid" => "Выплаченная сумма",
    "pay_salary" => "Выплатить зарплату",
    "salary_create" => "Добавить выплату",
    "salary_update" => "Редактировать выплату",
    "salary_delete" => "Удалить выплату",
    "salary_created_successfully" => "Выплата успешно добавлена",
    'select_worker' => 'Выберите сотрудника',
    'select_type' => 'Выберите тип',
    "all" => "Все",
    "search" => "Поиск",
    "remaining_amount" => "Оставшаяся сумма",
    "paid_amount" => "Выплаченная сумма",
    "worker_salary_amount" => "Месячная зарплата",
    "worker_salary_start_date" => "Дата начала зарплаты",
    "select_date" => "Выберите дату",
    "worker_created_successfully" => "Сотрудник успешно создан",
    "change_salary" => "Изменить зарплату",
    "current_salary" => "Текущая зарплата",
    "new_salary" => "Новая зарплата",
    "salary_start_date" => "Дата начала зарплаты",

    //3
    "worker_not_found" => "Сотрудник не найден",
    "salary_changed_successfully" => "Зарплата успешно изменена",
    "new_salary_date_must_be_greater" => "Дата начала новой зарплаты должна быть больше предыдущей!",
    "worker_info" => "Информация о сотруднике",
    "current_salary_info" => "Текущая информация о зарплате",
    "salary_history" => "История зарплат",
    "current" => "Текущий",
    "view" => "Просмотр",
    "no_salary_info" => "Информация о зарплате отсутствует",
    "view_salary" => "Информация о сотруднике",
    "start_date" => "Дата начала",
    "end_date" => "Дата окончания",
    "worker" => "Сотрудник",
    "expense_type_section" => "Раздел типов расходов",
    "expense_type_name" => "Название",
    "add_expense_type" => "Добавить тип расхода",
    "edit_expense_type" => "Редактировать тип расхода",
    "expense_type_delete" => "Удалить тип расхода",
    "Expense type created successfully." => "Тип расхода успешно создан.",
    "Expense type updated successfully." => "Тип расхода успешно обновлен.",
    "Expense type deleted successfully." => "Тип расхода успешно удален.",
    "Error deleting expense type" => "Ошибка при удалении типа расхода",
    "Expense type not found." => "Тип расхода не найден.",
    "clients_section" => "Раздел клиентов",
    "client_name" => "Имя клиента",
    "add_client" => "Добавить клиента",
    "edit_client" => "Редактировать клиента",
    "client_delete" => "Удалить клиента",
    "Client created successfully." => "Клиент успешно создан.",
    "Client updated successfully." => "Клиент успешно обновлен.",
    "Client deleted successfully." => "Клиент успешно удален.",
    "Error deleting client" => "Ошибка при удалении клиента",
    "Client not found." => "Клиент не найден.",
    "account_number" => "Номер счета",
    "select_region" => "Выберите регион",
    "client_region_id" => "Регион клиента",
    'Client Information' => 'Информация о клиенте',
    'Purchase History' => 'История покупок',
    'Payment History' => 'История платежей',
    'Balance History' => 'История баланса',
    'Additional Phone' => 'Дополнительный телефон',
    'Current Balance' => 'Текущий баланс',
    'Date' => 'Дата',
    'Product' => 'Продукт',
    'Quantity' => 'Количество',
    'Unit Price' => 'Цена за единицу',
    'Total Price' => 'Общая стоимость',
    'Status' => 'Статус',
    'Seller' => 'Продавец',
    'Added By' => 'Добавил',
    'Old Amount' => 'Старый баланс',
    'New Amount' => 'Новый баланс',
    "client_balance" => "Баланс клиента",
    'cash' => 'Наличные',
    'card' => 'Карта',

    //4
    'transfer' => 'Перевод',
    'pay' => 'Оплатить',
    'payment_created_successfully' => 'Платеж успешно добавлен',
    'payment_amount' => 'Сумма платежа',
    'payment_type' => 'Тип платежа',
    "client_pay" => "Оплата клиента",
    "client_region" => "Регион",
    "added_by" => "Добавил сотрудник",
    "suppliers" => "Поставщики",
    "add_supplier" => "Добавить поставщика",
    "edit_supplier" => "Редактировать поставщика",
    "supplier_delete" => "Удалить поставщика",
    "supplier_pay" => "Оплата поставщику",
    "balance" => "Баланс",
    "payments" => "Платежи",
    "balance_history" => "История баланса",
    "old_amount" => "Старая сумма",
    "new_amount" => "Новая сумма",
    "date" => "Дата",
    "suppliers_section" => "Раздел поставщиков",
    "supplier_full_name" => "Полное имя поставщика",
    "phone_number_error_length" => "Номер телефона должен содержать не менее 12 символов!",
    "phone_number_error" => "Код номера телефона не существует!",
    "Supplier Information" => "Информация о поставщике",
    "Supplier Payments" => "Платежи поставщика",
    "Supplier Balance History" => "История баланса поставщика",
    "Supplier created successfully." => "Поставщик успешно создан.",
    "Supplier updated successfully." => "Поставщик успешно обновлен.",
    "Supplier deleted successfully." => "Поставщик успешно удален.",
    "Error deleting supplier" => "Ошибка при удалении поставщика",
    "Supplier not found." => "Поставщик не найден.",
    "Supplier Payment created successfully." => "Платеж поставщику успешно добавлен.",
    "product_section" => "Раздел товаров",
    "product_name" => "Название товара",
    "product_price" => "Цена товара",
    "product_block_quantity" => "Количество в блоке",
    "add_product" => "Добавить товар",
    "edit_product" => "Редактировать товар",
    "product_delete" => "Удалить товар",
    "Product created successfully." => "Товар успешно создан.",
    "Product updated successfully." => "Товар успешно обновлен.",
    "Product deleted successfully." => "Товар успешно удален.",
    "Error deleting product" => "Ошибка при удалении товара",
    "Product not found." => "Товар не найден.",
    "change_price" => "Редактировать цену",
    "current_price" => "Текущая цена",
    "new_price" => "Новая цена",
    "start_date" => "Дата вступления в силу",
    "price_updated_successfully" => "Цена успешно обновлена",
    "Start date cannot be in the past" => "Нельзя установить прошедшую дату",
    "product_info" => "Информация о товаре",
    "price_history" => "История цен",
    "price" => "Цена",
    'Materials' => 'Сырье',
    'Create Material' => 'Добавить сырье',
    'Update Material' => 'Редактировать сырье',
    'Delete Material' => 'Удалить сырье',
    'Material created successfully.' => 'Сырье успешно добавлено.',
    'Material updated successfully.' => 'Сырье успешно обновлено.',
    'Material deleted successfully.' => 'Сырье успешно удалено.',
    'Are you sure you want to delete this material?' => 'Вы уверены, что хотите удалить это сырье?',
    'Total Quantity' => 'Общее количество',
    'Defect Quantity' => 'Количество брака',
    'Price' => 'Цена',
    'Total Sum' => 'Общая сумма',
    'Description' => 'Описание',
    'Name' => 'Название',

    //5
    'Close' => 'Закрыть',
    'Save' => 'Сохранить',
    'ID' => 'ID',
    "Equipments" => "Оборудование",
    "Create Equipment" => "Добавить оборудование",
    "Update Equipment" => "Редактировать оборудование",
    "Photo" => "Фото",
    "Initial Cost" => "Начальная стоимость",
    "Depreciation Cost" => "Стоимость амортизации",
    "Purchase Date" => "Дата покупки",
    "repair" => "В ремонте",
    "Equipment created successfully." => "Оборудование успешно добавлено.",
    "Equipment updated successfully." => "Оборудование успешно обновлено.",
    "Equipment deleted successfully." => "Оборудование успешно удалено.",
    "Error deleting equipment" => "Ошибка при удалении оборудования",
    "Equipment not found." => "Оборудование не найдено.",
    "Photo not found" => "Фото не найдено",
    "Error loading photo" => "Ошибка при загрузке фото",
    "delete_equipment" => "Удалить оборудование",
    "change_status" => "Изменить статус",
    "change_equipment_status" => "Изменить статус оборудования",
    "status_changed_successfully" => "Статус успешно изменен",
    "error_changing_status" => "Ошибка при изменении статуса",
    "select_status" => "Выберите статус",
    "write_off" => "Списать",
    "send_to_repair" => "Отправить в ремонт",
    "return_to_work" => "Вернуть в работу",
    "equipment_name" => "Название оборудования",
    "equipment_photo" => "Фото оборудования",
    "useful_life_years" => "Срок службы (лет)",
    "quantity" => "Количество",
    "select_supplier" => "Выберите поставщика",
    "select_material" => "Выберите сырье",
    "material_income" => "Приход сырья",
    "income_materials" => "Раздел сырья",
    "income_material" => "Ввод сырья",
    "_material_section" => "Раздел видов сырья",
    "add_material_type" => "Добавить сырье",
    "edit_material_type" => "Редактировать сырье",
    "material_type_delete" => "Удалить сырье",
    "material_name" => "Название сырья",
    "material_description" => "Описание сырья",
    "material_defect_section" => "Раздел бракованного сырья",
    "who_entered" => "Кто ввел",
    "expenses_section" => "Раздел расходов",
    "add_expense" => "Добавить расход",
    "edit_expense" => "Редактировать расход",
    "expense_delete" => "Удалить расход",
    "expense_name" => "Название расхода",
    "expense_description" => "Описание расхода",
    "expense_price" => "Сумма расхода",
    "select_expense_type" => "Выберите тип расхода",
    "Expense type" => "Тип расхода",
    "select_payment_type" => "Выберите тип платежа",
    "Payment type" => "Тип платежа",
    "sales_section" => "Раздел продаж",
    "sell_user" => "Продавец",
    "total_sum" => "Общая сумма",
    "total_quantity" => "Общее количество",
    "update_sale" => "Редактировать продажу",
    "Please select both start and end dates" => "Выберите начальную и конечную даты",
    "From Date" => "С даты",
    "To Date" => "По дату",
    "payments_section" => "Раздел платежей",
    "payment_delete" => "Удалить платеж",
    "supplier_finance" => "Финансы поставщика",
    "supplier_name" => "Имя поставщика",
    "car_number" => "Номер машины",

    //6
    'accepted_by' => 'Подтвердивший сотрудник',
    "total_amount" => "Общая сумма",
    "accepted_items" => "Принятые товары",
    "payment_deleted_successfully" => "Платеж успешно удален",
    "are_you_sure_delete" => "Вы уверены, что хотите удалить?",
    "error_occurred" => "Произошла ошибка",
    "edit_income_material" => "Редактировать сырье",
    "Useful Life Years" => "Срок службы",
    "You don\'t have permission to access this resource" => "У вас нет доступа к этому ресурсу!",
    "unknown_role" => "Роль не найдена",
    "Invalid username or password" => "Неверный логин или пароль!",
    "Login successful" => "Успешный вход!",
    "Validation error" => "Ошибка валидации!",
    "select_product" => "Выберите продукт",
    "which_product" => "К какому продукту относится?",
    "which_product_index" => "К какому продукту относится",
    "no_role_assigned_to_this_user" => "Пользователь не имеет назначенной роли!",
    "Your request was made with invalid credentials" => "Неверный формат токена!",
    "admin" => "Администратор",
    "raw_keeper" => "Кладовщик сырья",
    "manufacturer" => "Производитель",
    "product_keeper" => "Кладовщик продукции",
    "sales" => "Продавец",
    "security" => "Охранник",
    "salary_payments_history" => "История выплат зарплат",
    "payment_date" => "Дата платежа",
    "no_salary_payments" => "История платежей отсутствует",
    "Invoice History" => "История счетов-фактур",
    "Invoice Number" => "Номер счета-фактуры",
    "Total Amount" => "Общая сумма",
    "Accepted By" => "Подтвердивший сотрудник",
    "remainder_quantity" => "Остаток количества",
    "ingredients" => "Ингредиенты продукта",
    "add_product_ingredient" => "Добавить ингредиент",
    "Invalid data provided" => "Введены некорректные данные",
    "Product and materials are required" => "Продукт и материалы обязательны",
    "Materials and product are required" => "Продукт и материалы обязательны",
    "edit_product_ingredient" => "Редактировать ингредиенты продукта",
    "Product ingredients updated successfully" => "Ингредиенты продукта успешно обновлены",
    "Error saving ingredient" => "Ошибка при сохранении ингредиента",
    "product_ingredient_delete" => "Удалить ингредиент продукта",
    "invoice" => "Счет-фактура",
    "driver" => "Водитель",
    'confirm_user' => 'Подтвердивший сотрудник',
    "new_sales" => "Новые",
    "confirmed_sales" => "Проданные",
    "invoice_section" => "Раздел счетов-фактур",
    "add_invoice" => "Добавить счет-фактуру",
    "add_invoice_new" => "Создать новый",
    "select_client" => "Выберите клиента",
    "client" => "Клиент",

    //7
    "fill_all_fields" => "Введите все данные",
    "quantity_error" => "Количество должно быть больше 0!",
    "edit_invoice" => "Редактировать счет-фактуру",
    "delete_invoice" => "Удалить счет-фактуру",
    "view_invoice" => "Просмотр счета-фактуры",
    "Invoice Information" => "Информация о счете-фактуре",
    "products" => "Продукты",
    "in_progress_sales" => "В процессе",
    "tracking_section" => "Раздел отслеживания",
    "client_payments" => "Платежи клиентов",
    "supplier_payments" => "Платежи поставщикам",
    "worker_payments" => "Выплаты зарплат",
    "material_incomes" => "Приход сырья",
    "material_outcomes" => "Расход сырья",
    "type_payment" => "Тип платежа",
    "bonus" => "Бонус",
    "cashbox" => "Касса",
    "balance_in_cash" => "Наличные",
    "balance_in_card" => "Карта",
    "balance_in_transfer" => "Перевод",
    "cashbox_section" => "Раздел кассы",
    "add_transfer" => "Перевод",
    "cashbox_detail" => "История кассы",
    "back_to_cashbox" => "Вернуться в кассу",
    "added_user" => "Платеж совершил",
    "balance_transfer" => "Перевод средств",
    "select" => "Выбрать",
    "from_cashbox" => "С кассы",
    "to_cashbox" => "На кассу",
    "fromCashbox and toCashbox should be different" => "Тип кассы должен отличаться!",
    "Not enough funds on the source cashbox." => "Недостаточно средств на кассе!",
    "type_in" => "Приход",
    "type_out" => "Расход",
    "payment_created_at" => "Дата платежа",
    "expenses_created_at" => "Дата расхода",
    "expenses_type_created_at" => "Дата добавления типа расхода",
    "material_income_created_at" => "Дата прихода сырья",
    "payment type" => "Тип платежа",
    "Product already exists" => "Такой продукт уже существует!",
    "price_start_date" => "Дата начала действия цены",
    "added_date" => "Дата добавления",
    "Material already exists." => "Такое сырье уже существует!",
    "material_income_history_view" => "История прихода сырья",
    "material_added_date" => "Дата поступления сырья",
    "material_added_by" => "Добавил сотрудник",
    "material_outcome" => "Расход сырья",
    "material_income_to_storage" => "Приход сырья на склад",
    "Income to Production" => "Приход на производство",
    "Outcome from Production" => "Расход с производства",
    "You can only delete the last entry" => "Можно удалить только последнюю запись!",
    "select_type_status" => "Выберите статус",
    "where_from_defect_source" => "Раздел списанных товаров",
    "supliers" => "Поставщики",
    "supplier_material_income" => "Приход сырья от поставщика",
    "suplier_name" => "Имя поставщика",
    "arrival_time" => "Время прибытия",
    "leave_time" => "Время отбытия",
    "user_who_load" => "Сотрудник, загрузивший товар",
    "sell_history" => "История продаж",
    "expenses_name_already_exists" => "Такое название расхода уже существует!",
    "sales_view" => "Детали продаж",
    "sales_products" => "Продукты в продаже",
    "detail_info" => "Подробная информация",
    "sales_created_at" => "Дата продажи",
    "sales_start_date" => "Дата начала загрузки",
    "sales_end_date" => "Дата завершения загрузки",
    "unit_price" => "Цена за единицу",
    "total_price" => "Общая цена",

    //8
    "to_defect_created_at" => "Время списания в брак",
    "invoice_created_at" => "Время создания",
    "security_records" => "Записи охраны",
    "driver_full_name" => "ФИО водителя",
    "accepted_at" => "Время отправления",
    "security_records_created_at" => "Время прибытия",
    "_sales" => "Продажи",
    "debts" => "Задолженность",
    "supplier_debts" => "Задолженность перед поставщиками",
    "client_debts" => "Задолженность клиентов",
    "currency" => "Валюта",
    "currency_course" => "Курс валюты",
    "currency_section" => "Раздел валют",
    "currency_name" => "Название валюты",
    "course" => "Курс",
    "the course is fixed time" => "Дата начала действия курса",
    "edit_course" => "Обновить курс",
    "currency_history" => "История курса",
    "Back to Currency" => "Вернуться к валютам",
    "set_price" => "Установить цену",
    "accept_income" => "Подтвердить приход",
    "invoice_number" => "Номер счета-фактуры",
    "accepted_invoices" => "Неподтвержденные счета-фактуры",
    "materials" => "Сырье",
    'not_set' => 'Не установлено',
    "prices_saved_successfully" => "Цены успешно обновлены",
    "price_not_set" => "Цена не установлена",
    "invoice_accepted_success" => "Счет-фактура успешно подтвержден",
    "accept" => "Подтвердить",
    "invoice not found or accepted already" => "Счет-фактура не найдена или уже подтверждена",
    "quantity_unit" => "шт.",
    "select_currency" => "Выберите валюту",
    "In Production" => "В производстве",
    "source" => "Источник",
    "special_prices" => "Специальная цена для клиента",
    "create_special_price" => "Добавить специальную цену",
    "update_special_price" => "Обновить специальную цену",
    "special_price" => "Специальная цена",
    "next" => "Следующая",
    "previous" => "Предыдущая",
    "delete_special_price" => "Удалить специальную цену",
    "deleted_successfully" => "Успешно удалено!",
    "detail_special_price" => "История специальных цен",
    "Not enough product on stock" => "Недостаточно товара на складе!",
    "has_bonus" => "Бонус",
    "bonus_products" => "Бонусные товары",
    "debt" => "Задолженность",
    "Deduct from salary" => "Удержать из зарплаты",
    "debt_payment" => "Оплата долга",
    "one_time_payment" => "Разовый платеж",
    "cashier" => "Кассир",
    "cashier_section" => "Раздел кассира",
    "cashier_detail" => "История кассира",
    "accepted" => "Подтверждено",
    "not_accepted" => "Не подтверждено",
    "accept_expense" => "Подтвердить расход",
    "record_successfully_accepted" => "Расход подтвержден",
    "record_not_successfully_accepted" => "Ошибка подтверждения расхода",
    "course_not_found" => "Курс валюты не установлен!",
    "print_invoice" => "Распечатать",
    "cashbox_name" => "Название кассы",
    "terminal" => "Терминал",
    "product #" => "Товар #",
    "not found on the warehouse" => "Нет на складе!",
    "not enough on the warehouse (available: {quantity})" => "Недостаточно на складе! (в наличии: {quantity})",
    "The rate has been successfully updated for all related currencies." => "Курс успешно обновлен для всех связанных валют.",
    "do_you_accept_this_action?" => "Вы подтверждаете это действие?",
    "error_this_action" => "Ошибка при подтверждении операции!",
    "error" => "Ошибка",
    "dou_you_this_income?" => "Вы хотите подтвердить этот приход?",
    "material_returns" => "Возвращенное сырье",
    "remaind_salary_this_month" => "Оставшаяся зарплата за этот месяц",
    "amount_debt" => "Сумма долга",
    "export_excel" => "Отчет по сотрудникам",
    "export_excel_to_download" => "Сформировать отчет",
    "select_month" => "Выберите месяц",

    // Системные сообщения
    "session_expired" => "Ваша сессия истекла. Пожалуйста, войдите снова.",

    // Навигация
    "Chiqish" => "Выход",
    'Login' => "Логин",
    'Parol' => "Парол",
    'Kirish' => "Вход",
    'Logout' => "Выход",
    'Dashboard' => "Главная",
    'reports' => "Отчеты",
    'sales_report' => "Отчет по продажам",
    'page_not_found' => "Страница не найдена!",
    'go_to_home' => "Вернуться на главную",
    "free_product" => "Бесплато",
    "total" => "Сумма",
    'donation' => 'Штук',
    'block' => 'Блок',
    'sales_report_title' => 'Продажи',
    'all_products' => 'Все продукты',
    "sold_products_and_bonus" => "Продажи и бонусы",
    "free_products" => "Бесплатные продукты",
    "no_data_found" => "Данные не найдены",
    "free_product_created_at" => "Дата выдачи",
    "sale_date" => "Дата продажи",
    "quantity_block" => "Блок",
    "product_release" => "Выпуск продукта",
    "sell_price" => "Цена продажи",
    "select_driver" => "Выберите водителя",
    "add_new_driver" => "Добавить нового водителя",
    "enter_driver_name" => "Введите имя водителя",
    "enter_car_number" => "Введите номер машины",
    "please_enter_driver_name" => "Введите имя водителя",
    "please_select_client_first" => "Выберите клиента",
    "driver_name" => "Имя водителя",
    "invoice_not_found" => "Счет-фактура не найдена",
    'Pending' => 'В ожидании',
    'Approved' => 'Подтверждено',
    'Export to Excel' => 'Экспорт в Excel',
    'Not enough material "{material_name}" in stock. Available: {available}, Required: {required}' => 'Недостаточно материала #{material_name} на складе. Доступно: {available}, Требуется: {required}',
    'Material "{material_name}" not found in stock' => 'Материал "{material_name}" не найден на складе',
    'Invalid quantity data for material "{material_name}"' => 'Некорректные данные количества для материала "{material_name}"',
    'Material with ID #{material_id} not found in database' => 'Материал #{material_id} не найден в базе данных',
    'Product "{product_name}" not found on the warehouse' => 'Продукт "{product_name}" не найден на складе',
    'Not enough product "{product_name}" on the warehouse. Available: {available}, Required: {required}' => 'Недостаточно продукта "{product_name}" на складе. Доступно: {available}, Требуется: {required}',
    'Product "{product_name}" not found' => 'Продукт "{product_name}" не найден',
    "view_sales" => "Просмотр продаж",
    "back_to_invoice" => "Вернуться к счетам-фактурам",
    "box_count" => "Количество блоков",
    "is_fizzy" => "Газированный",
    "product_priority" => "Приоритет",
    "client_contract" => "Договор",
    "contract" => "Договор",
    "legal_address" => "Юридический адрес",
    "organization" => "Организация",
    "contract_date_start" => "Дата начала",
    "contract_date_end" => "Дата окончания",
    "contract_status" => "Статус",
    "product_return" => "Возврат продукта",
    "Quantity must be greater than zero" => "Количество должно быть больше нуля",
    "print_with_price" => "Печать с ценой",
    "print_without_price" => "Печать без цены",
    "order" => "Заказ",
    "at" => "в",
    "select_sales" => "Выберите продажу",
    "product_return_success" => "Продукт успешно возвращено",
    "returned" => "Возвращено",
    "driver_is_required" => "Необходимо выбрать водителя",
    "supervisor" => "Начальник",
    "update" => "Обновить",
    "payment_updated_successfully" => "Платеж успешно обновлен",
    "expense_updated_successfully" => "Расход успешно обновлен",
    'can_return' => 'Может делать возврат',
    'products_required' => "Необходимо указать продукты",
    "at_least_one_product_required" => "Необходимо указать хотя бы один продукт",
    'product_not_found' => 'Продукт не найден',
    'price_not_found_for_product' => 'Цена не найдена для продукта',
    'quantity_block_error' => 'Количество блоков должно быть больше нуля',
    'bonus_products_required_when_has_bonus_true' => 'Необходимо указать бонусные продукты',
    'material_categories' => 'Категория сырья',
    'add_category' => 'Добавить категорию',
    'edit_category' => 'Редактировать категорию',
    'category_delete' => 'Удалить категорию',
    'category_created_successfully' => 'Категория успешно создана',
    'category_updated_successfully' => 'Категория успешно обновлена',
    'category_deleted_successfully' => 'Категория успешно удалена',
    'category_name' => 'Название категории',
    'are_you_sure_delete_category' => 'Вы действительно хотите удалить категорию "{name}"?',
    'category' => 'Категория',
    'select_category' => 'Выберите категорию',
    'Select alternative materials that can be used if main materials are not available' => 'Выберите альтернативные материалы, которые можно использовать, если основные материалы не доступны',
    'alternative_ingredients' => 'Альтернативные материалы',
    'No alternative materials' => 'Нет альтернативных материалов',
    'for_this_product_no_main_ingredients' => 'Для этого продукта не найдены основные ингредиенты',
    'Not enough material "{material_name}" in production and no alternative available. Available: {available}, Required: {required}' => 'Недостаточно материала "{material_name}" на производстве и нет доступных альтернатив. Доступно: {available}, Требуется: {required}',
    'Not enough alternative material "{alt_name}" for "{original_name}". Available: {available}, Required: {required}' => 'Недостаточно альтернативного материала "{alt_name}" для "{original_name}". Доступно: {available}, Требуется: {required}',
    'dont_update_accepted_record' => 'Нельзя обновить подтвержденную запись',
    'dont_update_deleted_record' => 'Нельзя обновить удаленную запись',
    'record_not_found' => 'Запись не найдена',
    "back_to_part" => "Вернуться к запчасти",
    "status_before" => "Статус до",
    "status_after" => "Статус после",
    "spare_parts" => "Запчасти",
    "total_defect" => "Всего брака",
    "total_defect_operations" => "Всего операций брака",
    "current_stock" => "Текущий запас",
    "available_quantity" => "Доступное количество",
    "no_history_records_found" => "Нет истории записей",
    "movement_type" => "Тип движения",
    "price_per_unit" => "Цена за единицу",
    "total_amount" => "Общая сумма",
    "comment" => "Комментарий",
    "user" => "Пользователь",
    "date" => "Дата",
    "quantity" => "Количество",
    "income" => "Приход",
    "outcome" => "Расход",
    "defect" => "Брак",
    "last_purchase_date" => "Дата последней покупки",
    "part_name" => "Название запчасти",
    "equipment" => "Оборудование",
    "income_quantity" => "Количество прихода",
    "defect_quantity" => "Количество брака",
    "defect_reason" => "Причина брака",
    "quantity_required" => "Количество должно быть больше нуля",
    "quantity_required_positive" => "Количество должно быть больше нуля",
    "defect_quantity_exceeds_available" => "Количество брака превышает доступное количество",
    "defect_reason_required" => "Причина брака обязательна для заполнения",
    "defect_recorded_successfully" => "Брак успешно зарегистрирован",
    "Error occurred while processing defect" => "Произошла ошибка при обработке брака",
    "installed" => "Установлено",
    "mixed" => "Смешанный",
    "Total" => "Всего",
    "Assigned" => "Назначено",
    "Available" => "Доступно",
    "Insufficient quantity available. Available: {available}" => "Недостаточно доступного количества. Доступно: {available}",
    "Error creating part assignment: {errors}" => "Ошибка создания назначения запчасти: {errors}",
    "Assigned to equipment: {equipment} (Assignment ID: {id})" => "Назначено на оборудование: {equipment} (ID назначения: {id})",
    "Assigned {quantity} units to equipment: {equipment}" => "Назначено {quantity} единиц на оборудование: {equipment}",
    "installation" => "Установка",

    // Equipment parts defect translations
    "defect_quantity_exceeds_assigned" => "Количество для брака превышает закрепленное ({available} доступно)",
    "assigned_quantity" => "Закрепленное количество",
    "units_assigned_to_equipment" => "единиц закреплено за оборудованием",
    "max_available_for_defect" => "Максимум доступно для брака: {max}",
    "Part is not assigned to this equipment" => "Запчасть не закреплена за данным оборудованием",
    "Error loading defect form" => "Ошибка загрузки формы брака",
    "Defect: {quantity} units removed from equipment: {equipment}" => "Брак: {quantity} единиц снято с оборудования: {equipment}",
    "Error creating defect movement: {errors}" => "Ошибка создания движения брака: {errors}",

    // Reserve, repair, activate translations
    "move_to_reserve" => "Вернуть на склад",
    "send_to_repair" => "Отправить в ремонт",
    "return_to_work" => "Вернуть в работу",
    "warehouse_return_info" => "Запчасть будет снята с оборудования и возвращена на склад.",
    "repair_part_info" => "Запчасть будет отмечена как находящаяся в ремонте вместе с оборудованием.",
    "activate_part_info" => "Запчасть будет возвращена в активное состояние на оборудовании.",
    "repair_reason" => "Причина ремонта",
    "describe_repair_reason" => "Опишите причину отправки в ремонт",
    "optional_comment" => "Дополнительный комментарий (необязательно)",
    "Part returned to warehouse from equipment: {equipment}" => "Запчасть возвращена на склад с оборудования: {equipment}",
    "Part sent to repair with equipment: {equipment}" => "Запчасть отправлена в ремонт с оборудованием: {equipment}",
    "Part returned to work on equipment: {equipment}" => "Запчасть возвращена в работу на оборудовании: {equipment}",
    "Part returned to warehouse successfully" => "Запчасть успешно возвращена на склад",
    "Part sent to repair successfully" => "Запчасть успешно отправлена в ремонт",
    "Part returned to work successfully" => "Запчасть успешно возвращена в работу",
    "Part is not in repair for this equipment" => "Запчасть не находится в ремонте для данного оборудования",
    "Error loading reserve form" => "Ошибка загрузки формы возврата на склад",
    "Error loading repair form" => "Ошибка загрузки формы ремонта",
    "Error loading activate form" => "Ошибка загрузки формы активации",
    "Error occurred while processing reserve" => "Ошибка при обработке возврата на склад",
    "Error occurred while processing repair" => "Ошибка при обработке ремонта",
    "Error occurred while processing activation" => "Ошибка при обработке активации",

    // Partial repair/return translations
    "repair_quantity" => "Количество для ремонта",
    "return_quantity" => "Количество для возврата",
    "units_in_repair" => "единиц в ремонте",
    "max_available_for_repair" => "Максимум доступно для ремонта: {max}",
    "max_available_for_return" => "Максимум доступно для возврата: {max}",
    "repair_quantity_exceeds_assigned" => "Количество для ремонта превышает закрепленное количество. Доступно: {available}",
    "return_quantity_exceeds_repair" => "Количество для возврата превышает количество в ремонте. Доступно: {available}",
    "all_quantity_will_be_sent_to_repair" => "Все количество будет отправлено в ремонт",
    "all_quantity_will_be_returned_to_work" => "Все количество будет возвращено в работу",

    // History action labels
    "installed" => "Установлена",
    "removed" => "Снята",
    "moved_to_reserve" => "Перемещена в резерв",
    "created" => "Создана",
    "sent_to_repair" => "Отправлена в ремонт",
    "returned_from_repair" => "Возвращена из ремонта",
    "installation" => "Установка",
    "removal" => "Снятие",
    "reserve" => "Резерв",
    "creation" => "Создание",
    "action" => "Действие",
    "reason" => "Причина",
    "describe_reason" => "Опишите причину",
];
