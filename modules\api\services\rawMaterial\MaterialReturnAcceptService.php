<?php

namespace app\modules\api\services\rawMaterial;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\MaterialStorage;
use app\common\models\MaterialStorageHistory;
use app\common\models\Tracking;
use app\common\models\SupplierBalance;
use app\common\models\SupplierBalanceHistory;
use app\common\models\InvoiceDetail;
use yii\base\Component;

/**
 * Сервис для подтверждения возврата материалов на склад
 */
class MaterialReturnAcceptService extends Component
{
    /**
     * Подтверждение возврата материалов на склад
     *
     * @param int $groupId ID группы материалов для возврата
     * @return array Результат операции
     */
    public function acceptMaterialReturn($groupId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            // Находим группу материалов
            $materialStatusGroup = MaterialStatusGroup::findOne([
                'id' => $groupId,
                'status' => MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER,
                'deleted_at' => null
            ]);

            if (!$materialStatusGroup) {
                throw new \Exception('Группа возврата материалов не найдена');
            }

            // Проверяем, что группа еще не подтверждена
            if ($materialStatusGroup->accepted_user_id !== null) {
                throw new \Exception('Группа возврата уже подтверждена');
            }

            // Получаем все материалы из группы
            $materials = MaterialStatus::find()
                ->where([
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ])
                ->all();

            if (empty($materials)) {
                throw new \Exception('Материалы в группе не найдены');
            }

            // Обрабатываем каждый материал
            foreach ($materials as $materialStatus) {
                // Находим или создаем запись на складе
                $storage = MaterialStorage::findOne([
                    'material_id' => $materialStatus->material_id,
                    'deleted_at' => null
                ]);

                if (!$storage) {
                    throw new \Exception("Материал с ID {$materialStatus->material_id} не найден на складе");
                }

                // Проверяем, достаточно ли материала на складе для возврата
                if ($storage->quantity < $materialStatus->quantity) {
                    throw new \Exception("Недостаточно материала на складе для возврата. Доступно: {$storage->quantity}, требуется: {$materialStatus->quantity}");
                }

                // Уменьшаем количество на складе (возвращаем материал поставщику)
                $storage->quantity -= $materialStatus->quantity;

                if (!$storage->save()) {
                    throw new \Exception('Ошибка обновления склада: ' . json_encode($storage->getErrors()));
                }

                // Записываем в историю склада (расход - возврат поставщику)
                $history = new MaterialStorageHistory();
                $history->material_storage_id = $storage->id;
                $history->material_id = $materialStatus->material_id;
                $history->quantity = $materialStatus->quantity;
                $history->add_user_id = Yii::$app->user->id;
                $history->type = MaterialStorageHistory::TYPE_OUTCOME;
                $history->created_at = date('Y-m-d H:i:s');

                if (!$history->save()) {
                    throw new \Exception('Ошибка сохранения истории склада: ' . json_encode($history->getErrors()));
                }
            }

            // Рассчитываем общую сумму возврата для обновления баланса поставщика
            $totalReturnAmount = $this->calculateReturnAmount($materials);

            // Обновляем баланс поставщика (уменьшаем на сумму возврата)
            if ($materialStatusGroup->supplier_id && $totalReturnAmount > 0) {
                $this->updateSupplierBalance($materialStatusGroup->supplier_id, $totalReturnAmount);
            }

            // Подтверждаем группу возврата
            $materialStatusGroup->accepted_user_id = Yii::$app->user->id;
            $materialStatusGroup->accepted_at = date('Y-m-d H:i:s');

            if (!$materialStatusGroup->save()) {
                throw new \Exception('Ошибка подтверждения группы возврата: ' . json_encode($materialStatusGroup->getErrors()));
            }

            // Логируем действие
            ActionLogger::actionLog(
                'accept_material_return',
                'material_status_group',
                $groupId,
                [
                    'materials_count' => count($materials),
                    'total_quantity' => array_sum(array_column($materials, 'quantity'))
                ]
            );

            $transaction->commit();

            return [
                'success' => true,
                'message' => 'Возврат материалов успешно подтвержден'
            ];

        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Рассчитывает общую сумму возврата материалов
     *
     * @param array $materials Массив материалов
     * @return float Общая сумма возврата
     */
    private function calculateReturnAmount($materials)
    {
        $totalAmount = 0;

        foreach ($materials as $materialStatus) {
            // Получаем последнюю цену материала из invoice_detail
            $latestPrice = InvoiceDetail::find()
                ->select('price')
                ->where(['material_id' => $materialStatus->material_id])
                ->andWhere(['!=', 'price', null])
                ->andWhere(['deleted_at' => null])
                ->orderBy(['id' => SORT_DESC])
                ->scalar();

            if ($latestPrice) {
                $totalAmount += $latestPrice * $materialStatus->quantity;
            }
        }

        return $totalAmount;
    }

    /**
     * Обновляет баланс поставщика при возврате материалов
     *
     * @param int $supplierId ID поставщика
     * @param float $amount Сумма для списания
     * @throws \Exception
     */
    private function updateSupplierBalance($supplierId, $amount)
    {
        // Находим или создаем баланс поставщика
        $supplierBalance = SupplierBalance::findOne(['supplier_id' => $supplierId]);
        if (!$supplierBalance) {
            $supplierBalance = new SupplierBalance();
            $supplierBalance->supplier_id = $supplierId;
            $supplierBalance->amount = 0;
        }

        $oldAmount = $supplierBalance->amount;

        // Уменьшаем баланс поставщика (разрешаем отрицательный баланс)
        $supplierBalance->amount -= $amount;
        $supplierBalance->updated_at = date('Y-m-d H:i:s');

        if (!$supplierBalance->save()) {
            throw new \Exception('Ошибка обновления баланса поставщика: ' . json_encode($supplierBalance->getErrors()));
        }

        // Создаем запись в истории баланса
        $balanceHistory = new SupplierBalanceHistory();
        $balanceHistory->supplier_id = $supplierId;
        $balanceHistory->amount = $supplierBalance->amount;
        $balanceHistory->old_amount = $oldAmount;
        $balanceHistory->type = SupplierBalanceHistory::TYPE_MATERIAL_RETURN;
        $balanceHistory->created_at = date('Y-m-d H:i:s');

        if (!$balanceHistory->save()) {
            throw new \Exception('Ошибка сохранения истории баланса поставщика: ' . json_encode($balanceHistory->getErrors()));
        }
    }
}
