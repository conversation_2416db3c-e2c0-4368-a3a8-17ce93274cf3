<?php

namespace app\modules\api\services\manufacter;

use Yii;
use app\common\models\ActionLogger;
use app\common\models\MaterialDefect;
use app\common\models\MaterialProduction;
use app\common\models\MaterialStatusGroup;
use app\common\models\MaterialStatus;
use app\common\models\Tracking;
use yii\base\Component;

/**
 * Сервис для удаления записи о браке материалов в процессе производства
 */
class DeleteSendToMaterialDefectService extends Component
{
    /**
     * Удаление записи о браке материала или возврате
     *
     * @param int $id ID записи (material_defect_id или group_id)
     * @param string $type Тип записи: 'defect' или 'return'
     * @return array Результат операции
     */
    public function deleteMaterialDefect($id, $type = 'defect')
    {
        if (!$id) {
            return [
                'success' => false,
                'message' => 'ID записи обязателен'
            ];
        }

        if ($type === 'defect') {
            return $this->deleteMaterialDefectRecord($id);
        } elseif ($type === 'return') {
            return $this->deleteMaterialReturnRecord($id);
        }

        return [
            'success' => false,
            'message' => 'Неизвестный тип записи'
        ];
    }

    /**
     * Удаление записи о браке материала
     */
    private function deleteMaterialDefectRecord($materialDefectId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $materialDefect = MaterialDefect::findOne([
                'id' => $materialDefectId,
                'deleted_at' => null,
                'add_user_id' => Yii::$app->user->id
            ]);

            if (!$materialDefect) {
                throw new \Exception('Запись не найдена или уже подтверждена');
            }

            // Проверка времени - можно удалять только в течение 24 часов
            $createdTime = strtotime($materialDefect->created_at);
            $currentTime = time();
            $timeDifference = $currentTime - $createdTime;

            if ($timeDifference > 86400) { // 86400 секунд = 24 часа
                throw new \Exception('Удаление возможно только в течение 24 часов с момента создания');
            }

            $tracking = Tracking::find()
                ->where([
                    'process_id' => $materialDefectId,
                    'progress_type' => Tracking::TYPE_MATERIAL_DEFECT,
                    'deleted_at' => null
                ])
                ->one();

            if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
                throw new \Exception('Материал уже подтвержден, удаление невозможно');
            }

            if ($tracking) {
                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception('Ошибка при удалении записи tracking');
                }
            }

            $materialDefect->deleted_at = date('Y-m-d H:i:s');
            if (!$materialDefect->save()) {
                throw new \Exception('Ошибка при удалении записи о браке');
            }

            ActionLogger::actionLog(
                'delete_send_to_defect',
                'material_defect',
                $materialDefect->id,
                [
                    'material_id' => $materialDefect->material_id,
                    'quantity' => $materialDefect->quantity,
                    'description' => $materialDefect->description,
                    'is_processed' => $materialDefect->is_processed
                ]
            );

            $transaction->commit();
            return [
                'success' => true,
                'message' => 'Запись о браке успешно удалена'
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }

    /**
     * Удаление записи о возврате материала
     */
    private function deleteMaterialReturnRecord($groupId)
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $materialStatusGroup = MaterialStatusGroup::findOne([
                'id' => $groupId,
                'deleted_at' => null,
                'status' => MaterialStatusGroup::STATUS_RETURNED_TO_SUPPLIER,
                'add_user_id' => Yii::$app->user->id
            ]);

            if (!$materialStatusGroup) {
                throw new \Exception('Запись о возврате не найдена');
            }

            // Проверка времени - можно удалять только в течение 24 часов
            $createdTime = strtotime($materialStatusGroup->created_at);
            $currentTime = time();
            $timeDifference = $currentTime - $createdTime;

            if ($timeDifference > 86400) { // 86400 секунд = 24 часа
                throw new \Exception('Удаление возможно только в течение 24 часов с момента создания');
            }

            // Проверяем статус в Tracking
            $tracking = Tracking::find()
                ->where([
                    'process_id' => $groupId,
                    'progress_type' => Tracking::TYPE_MATERIAL_RETURN,
                    'deleted_at' => null
                ])
                ->one();

            if ($tracking && $tracking->status === Tracking::STATUS_ACCEPTED) {
                throw new \Exception('Возврат уже подтвержден, удаление невозможно');
            }

            // Удаляем связанные записи MaterialStatus
            $materialStatuses = MaterialStatus::find()
                ->where([
                    'status_group_id' => $groupId,
                    'deleted_at' => null
                ])
                ->all();

            foreach ($materialStatuses as $materialStatus) {
                $materialStatus->delete();
            }

            // Удаляем tracking запись
            if ($tracking) {
                $tracking->deleted_at = date('Y-m-d H:i:s');
                if (!$tracking->save()) {
                    throw new \Exception('Ошибка при удалении записи tracking');
                }
            }

            // Удаляем группу
            $materialStatusGroup->deleted_at = date('Y-m-d H:i:s');
            if (!$materialStatusGroup->save()) {
                throw new \Exception('Ошибка при удалении записи о возврате');
            }

            ActionLogger::actionLog(
                'delete_material_return',
                'material_status_group',
                $materialStatusGroup->id,
                [
                    'status' => $materialStatusGroup->status,
                    'materials_count' => count($materialStatuses)
                ]
            );

            $transaction->commit();
            return [
                'success' => true,
                'message' => 'Запись о возврате успешно удалена'
            ];
        } catch (\Exception $e) {
            $transaction->rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage()
            ];
        }
    }
}
