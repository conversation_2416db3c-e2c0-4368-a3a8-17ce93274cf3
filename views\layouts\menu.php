<?php

use yii\helpers\Html;
use yii\helpers\Url;
?>

<style>
/* Отключаем стрелку после пункта меню с выпадающим списком */
.nav-link.has-dropdown::after {
    display: none !important;
}

/* Увеличиваем специфичность селектора для выпадающего меню */
.navbar .nav-item .dropdown-menu {
    min-width: 250px !important; /* Увеличиваем минимальную ширину */
    max-width: 350px !important; /* Ограничиваем максимальную ширину */
    width: auto !important; /* Позволяем ширине адаптироваться к содержимому */
    white-space: normal !important; /* Разрешаем перенос текста */
    overflow-wrap: break-word !important; /* Перенос длинных слов */
    padding: 0.5rem 0 !important; /* Отступы для читаемости */
}

/* Стили для элементов внутри выпадающего меню */
.navbar .nav-item .dropdown-menu .dropdown-item {
    padding: 0.5rem 1rem !important; /* Увеличиваем отступы */
    white-space: normal !important; /* Убеждаемся, что текст переносится */
    overflow-wrap: break-word !important; /* Перенос слов */
    line-height: 1.5 !important; /* Улучшаем читаемость */
}

/* Убеждаемся, что родительский контейнер не обрезает меню */
.navbar-nav, .nav-item, .nav-collapse {
    overflow: visible !important; /* Предотвращаем обрезку */
}


</style>


<div class="navbar-bg" style="height: 70px;"></div>
    <nav class="navbar navbar-expand-lg main-navbar">
        <a href="<?= Url::home() ?>" class="navbar-brand sidebar-gone-hide"><?php echo Yii::$app->params['projectName']; ?></a>
        <a href="#" class="nav-link sidebar-gone-show" data-toggle="sidebar"><i class="fas fa-bars"></i></a>
        <div class="nav-collapse">
            <a class="sidebar-gone-show nav-collapse-toggle nav-link" href="#">
                <i class="fas fa-ellipsis-v"></i>
            </a>

            <ul class="navbar-nav">

                <?php if (Yii::$app->user->can('sales')): ?>
                    <!-- Меню только для sales -->
                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','invoice') ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/sales-invoice/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','client_invoice') ?></a>
                            </li>
                        </ul>
                    </li>
                <?php endif; ?>



                <?php if (Yii::$app->user->can('admin')): ?>
                <!-- Остальное меню только для админа -->
                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','processes') ?></span>
                        </a>

                        <ul class="dropdown-menu">

                                <li class="nav-item"><a href="<?php
                                    echo Url::to(['/backend/tracking/index']); ?>" class="nav-link text-primary">
                                        <?php echo Yii::t('app','processes') ?>
                                    </a>
                                </li>

                                <li class="nav-item"><a href="<?php
                                    echo Url::to(['/backend/debt/index']); ?>" class="nav-link text-primary">
                                        <?php echo Yii::t('app','debts') ?>
                                    </a>
                                </li>

                        </ul>
                    </li>



                    <!-- Excel clients menu item removed -->











                <li class="nav-item dropdown">
                    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                        <span><?php echo Yii::t('app','clients') ?></span>
                    </a>
                    <ul class="dropdown-menu">

                        <li class="nav-item"><a href="<?php
                            echo Url::to(['/backend/client/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','client_section') ?></a>
                        </li>

                        <li class="nav-item"><a href="<?php
                            echo Url::to(['/backend/client-special-prices/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','special_prices') ?></a>
                        </li>

                    </ul>
                </li>



                <li class="nav-item dropdown">
                    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                        <span><?php echo Yii::t('app','salesSection') ?></span>
                    </a>
                    <ul class="dropdown-menu">

                        <li class="nav-item"><a href="<?php
                            echo Url::to(['/backend/sales/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','sales') ?></a>
                        </li>


                    </ul>
                </li>


                <li class="nav-item dropdown">
                    <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                        <span><?php echo Yii::t('app','finance') ?></span>
                    </a>
                    <ul class="dropdown-menu">

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/currency/view']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','currency') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/cashbox/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','cashbox') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/payment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','pays') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/expenses/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','expenses') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/expenses-type/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','expense_type') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/cashier/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','cashier') ?></a>
                            </li>



                    </ul>
                </li>

                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','supplier') ?></span>
                        </a>
                        <ul class="dropdown-menu">

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/supplier/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/supplier-finance/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier_material_income') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/supplier-finance/payments']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','supplier_finance') ?></a>
                            </li>

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/material-return/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_return') ?></a>
                            </li>


                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','storage') ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/product/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','product') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/material-income/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_income') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/material/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/equipment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','equipment') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/equipment-part/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','equipment_parts') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/material-category/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','material_categories') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/equipment/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','equipment') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/material-defect/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','defect') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/product-ingredients/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','ingredients') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/inventory-adjustment/index']); ?>" class="nav-link text-primary">Корректировка остатков</a>
                            </li>
                        </ul>
                    </li>


                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','invoice') ?></span>
                        </a>
                        <ul class="dropdown-menu">

                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/invoice/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','invoice') ?></a>
                            </li>

                        </ul>
                    </li>


                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','settings') ?></span>
                        </a>
                        <ul class="dropdown-menu">
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/user/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','users') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/region/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','region') ?></a>
                            </li>
                            <li class="nav-item"><a href="<?php
                                echo Url::to(['/backend/position/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','position') ?></a>
                            </li>


                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','workers') ?></span>
                        </a>
                        <ul class="dropdown-menu">

                                <li class="nav-item"><a href="<?php
                                    echo Url::to(['/backend/worker/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','workers') ?></a>
                                </li>
                                <li class="nav-item"><a href="<?php
                                    echo Url::to(['/backend/worker-finance/index']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','processes') ?></a>
                                </li>


                        </ul>
                    </li>

                    <li class="nav-item dropdown">
                        <a href="#" data-toggle="dropdown" class="nav-link has-dropdown" aria-expanded="false">
                            <span><?php echo Yii::t('app','reports') ?></span>
                        </a>
                        <ul class="dropdown-menu">
                                <li class="nav-item"><a href="<?php
                                    echo Url::to(['/backend/report/sales']); ?>" class="nav-link text-primary"><?php echo Yii::t('app','sales_report') ?></a>
                                </li>
                        </ul>
                    </li>

                <?php endif; ?>

            </ul>
        </div>

        <ul class="navbar-nav navbar-right ml-auto">
            <!-- Переключатель языка -->
            <li class="dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg">
                    <i class="fas fa-language"></i>
                    <div class="d-sm-none d-lg-inline-block">
                        <?php echo strtoupper(Yii::$app->language); ?>
                    </div>
                </a>
                <div class="dropdown-menu dropdown-menu-right">
                    <?= Html::a('Русский', ['/site/change-language', 'language' => 'ru'], [
                        'class' => 'dropdown-item' . (Yii::$app->language == 'ru' ? ' active' : ''),
                        'data-method' => 'post',
                    ]) ?>
                    <?= Html::a('Ўзбекча', ['/site/change-language', 'language' => 'uz'], [
                        'class' => 'dropdown-item' . (Yii::$app->language == 'uz' ? ' active' : ''),
                        'data-method' => 'post',
                    ]) ?>
                </div>
            </li>

            <li class="dropdown">
                <a href="#" data-toggle="dropdown" class="nav-link dropdown-toggle nav-link-lg nav-link-user">
                    <div class="d-sm-none d-lg-inline-block">
                        <?php echo ((isset(Yii::$app->user->identity)) ? Yii::$app->user->identity->username : '') ?>
                    </div>
                </a>

                <div class="dropdown-menu dropdown-menu-right">
                    <?php if (Yii::$app->user->isGuest): ?>
                        <?= Html::a('<i class="fas fa-sign-in-alt"></i> Login', ['/site/login'], ['class' => 'dropdown-item has-icon']) ?>
                    <?php else: ?>
                        <?= Html::a(
                            '<i class="fas fa-sign-out-alt"></i> ' . Yii::t('app', 'Chiqish'),
                            ['/site/logout'],
                            [
                                'class' => 'dropdown-item has-icon text-danger',
                                'data-method' => 'post',
                            ]
                        ) ?>
                    <?php endif; ?>
                </div>
            </li>
        </ul>
    </nav>
