<?php
use yii\helpers\Html;
use yii\helpers\Url;
use yii\web\JqueryAsset;
use yii\bootstrap4\BootstrapAsset;

/* @var $this yii\web\View */
/* @var $returnGroups array */

$this->title = Yii::t('app', 'material_return');
$this->params['breadcrumbs'][] = $this->title;

// Регистрируем jQuery и Bootstrap
JqueryAsset::register($this);
BootstrapAsset::register($this);
$this->registerMetaTag([
    'name' => 'csrf-token',
    'content' => Yii::$app->request->csrfToken,
]);

?>

<div class="material-return-index">
    <!-- Ваш HTML-код без изменений -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-undo-alt"></i>
                        <?= Html::encode($this->title) ?>
                    </h3>
                </div>
                <div class="card-body">
                    <?php if (empty($returnGroups)): ?>
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            <?= Yii::t('app', 'no_pending_returns') ?>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th><?= Yii::t('app', 'created_at') ?></th>
                                        <th><?= Yii::t('app', 'user') ?></th>
                                        <th><?= Yii::t('app', 'supplier') ?></th>
                                        <th><?= Yii::t('app', 'materials') ?></th>
                                        <th><?= Yii::t('app', 'total_sum') ?></th>
                                        <th><?= Yii::t('app', 'actions') ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($returnGroups as $group): ?>
                                        <tr>
                                            <td>
                                                <span class="badge badge-secondary">
                                                    <?= date('d.m.Y H:i', strtotime($group['created_at'] ?? 'now')) ?>
                                                </span>
                                            </td>
                                            <td>
                                                <i class="fas fa-user"></i>
                                                <?= Html::encode($group['user_name'] ?? 'Неизвестный пользователь') ?>
                                            </td>
                                            <td>
                                                <i class="fas fa-truck"></i>
                                                <?= Html::encode($group['supplier_name'] ?? 'Неизвестный поставщик') ?>
                                            </td>
                                            <td>
                                                <div class="materials-list">
                                                    <?php if (is_array($group['materials']) && !empty($group['materials'])): ?>
                                                        <?php foreach ($group['materials'] as $material): ?>
                                                            <div class="material-item mb-1">
                                                                <span class="badge badge-light">
                                                                    <?= Html::encode($material['material_name'] ?? 'Неизвестный материал') ?>
                                                                </span>
                                                                <span class="text-muted">
                                                                    <?= number_format($material['quantity'] ?? 0, 2) ?>
                                                                    <?= Html::encode($material['unit_type_name'] ?? 'шт') ?>
                                                                </span>
                                                                <?php if (($material['unit_price'] ?? 0) > 0): ?>
                                                                    <span class="text-success">
                                                                        (<?= number_format($material['total_price'] ?? 0, 2) ?> сум)
                                                                    </span>
                                                                <?php endif; ?>
                                                            </div>
                                                        <?php endforeach; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">Нет материалов</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="badge badge-warning">
                                                    <?= number_format($group['total_sum'] ?? 0, 2) ?> сум
                                                </span>
                                            </td>
                                            <td>
                                                <button type="button"
                                                        class="btn btn-success btn-sm accept-return-btn"
                                                        data-group-id="<?= $group['id'] ?? 0 ?>"
                                                        data-supplier="<?= Html::encode($group['supplier_name'] ?? 'Неизвестный поставщик') ?>"
                                                        data-total="<?= number_format($group['total_sum'] ?? 0, 2) ?>">
                                                    <i class="fas fa-check"></i>
                                                    <?= Yii::t('app', 'accept') ?>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Модальное окно подтверждения -->
<div class="modal fade" id="confirmModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle"></i>
                    <?= Yii::t('app', 'confirm_action') ?>
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>×</span>
                </button>
            </div>
            <div class="modal-body">
                <p id="confirmText"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                    <?= Yii::t('app', 'cancel') ?>
                </button>
                <button type="button" class="btn btn-success" id="confirmAcceptBtn">
                    <i class="fas fa-check"></i>
                    <?= Yii::t('app', 'accept') ?>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.materials-list {
    max-width: 300px;
}

.material-item {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 5px;
}

.material-item .badge {
    font-size: 0.8em;
}

.table td {
    vertical-align: middle;
}
</style>

<?php
$this->registerJs(<<<JS
$(document).ready(function() {
    let currentGroupId = null;

    $('.accept-return-btn').on('click', function() {
        currentGroupId = $(this).data('group-id');
        const supplier = $(this).data('supplier');
        const total = $(this).data('total');

        $('#confirmText').html(
            `Подтвердить возврат материалов поставщику <strong>\${supplier}</strong> на сумму <strong>\${total} сум</strong>?<br><br>` +
            `<small class="text-muted">Материалы будут списаны со склада и возвращены поставщику, а с баланса поставщика будет списана указанная сумма.</small>`
        );

        $('#confirmModal').modal('show');
    });

    $('#confirmAcceptBtn').on('click', function() {
        if (!currentGroupId) return;

        const \$btn = $(this);
        const originalText = \$btn.html();

        \$btn.html('<i class="fas fa-spinner fa-spin"></i> Обработка...');
        \$btn.prop('disabled', true);

        $.ajax({
            url: '<?= Url::to(['material-return/accept']) ?>',
            type: 'POST',
            data: {
                group_id: currentGroupId,
                _csrf: $('meta[name=csrf-token]').attr('content')
            },
            success: function(response) {
                if (response.status === 'success') {
                    if (typeof toastr !== 'undefined') {
                        toastr.success(response.message);
                    } else {
                        alert(response.message);
                    }
                    setTimeout(function() {
                        location.reload();
                    }, 1500);
                } else {
                    if (typeof toastr !== 'undefined') {
                        toastr.error(response.message || 'Произошла ошибка');
                    } else {
                        alert(response.message || 'Произошла ошибка');
                    }
                }
            },
            error: function(xhr) {
                let errorMessage = 'Произошла ошибка при обработке запроса';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                if (typeof toastr !== 'undefined') {
                    toastr.error(errorMessage);
                } else {
                    alert(errorMessage);
                }
            },
            complete: function() {
                \$btn.html(originalText);
                \$btn.prop('disabled', false);
                $('#confirmModal').modal('hide');
                currentGroupId = null;
            }
        });
    });
});
JS, \yii\web\View::POS_READY);
?>