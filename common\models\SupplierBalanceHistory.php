<?php

namespace app\common\models;

use Yii;

/**
 * This is the model class for table "supplier_balance_history".
 *
 * @property int $id
 * @property int $supplier_id
 * @property float $amount
 * @property float $old_amount
 * @property int $type
 * @property string|null $created_at
 * @property string|null $deleted_at
 *
 * @property Supplier $supplier
 */
class SupplierBalanceHistory extends \yii\db\ActiveRecord
{
    // Типы операций с балансом поставщика
    const TYPE_PAYMENT_CASH = 1;        // Оплата наличными
    const TYPE_PAYMENT_CARD = 2;        // Оплата картой
    const TYPE_PAYMENT_TRANSFER = 3;    // Оплата переводом
    const TYPE_MATERIAL_RETURN = 4;     // Возврат материалов

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'supplier_balance_history';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['supplier_id', 'amount', 'old_amount', 'type'], 'required'],
            [['supplier_id', 'type'], 'integer'],
            [['amount', 'old_amount'], 'number'],
            [['created_at', 'deleted_at'], 'safe'],
            [['supplier_id'], 'exist', 'skipOnError' => true, 'targetClass' => Supplier::class, 'targetAttribute' => ['supplier_id' => 'id']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'supplier_id' => 'Supplier ID',
            'amount' => 'Amount',
            'old_amount' => 'Old Amount',
            'type' => 'Type',
            'created_at' => 'Created At',
        ];
    }

    /**
     * Gets query for [[Supplier]].
     *
     * @return \yii\db\ActiveQuery
     */
    public function getSupplier()
    {
        return $this->hasOne(Supplier::class, ['id' => 'supplier_id']);
    }
}
