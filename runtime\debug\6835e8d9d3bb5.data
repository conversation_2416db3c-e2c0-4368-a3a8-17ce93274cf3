a:14:{s:6:"config";s:4971:"a:5:{s:10:"phpVersion";s:5:"8.1.9";s:10:"yiiVersion";s:10:"2.0.52-dev";s:11:"application";a:8:{s:3:"yii";s:10:"2.0.52-dev";s:4:"name";s:14:"My Application";s:7:"version";s:3:"1.0";s:8:"language";s:2:"uz";s:14:"sourceLanguage";s:5:"en-US";s:7:"charset";s:5:"UTF-8";s:3:"env";s:3:"dev";s:5:"debug";b:1;}s:3:"php";a:5:{s:7:"version";s:5:"8.1.9";s:6:"xdebug";b:0;s:3:"apc";b:0;s:8:"memcache";b:0;s:9:"memcached";b:0;}s:10:"extensions";a:16:{s:25:"kartik-v/yii2-krajee-base";a:3:{s:4:"name";s:25:"kartik-v/yii2-krajee-base";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/base";s:67:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-krajee-base/src";}}s:20:"kartik-v/yii2-dialog";a:3:{s:4:"name";s:20:"kartik-v/yii2-dialog";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:14:"@kartik/dialog";s:62:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-dialog/src";}}s:18:"kartik-v/yii2-grid";a:3:{s:4:"name";s:18:"kartik-v/yii2-grid";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:12:"@kartik/grid";s:60:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-grid/src";}}s:35:"kartik-v/yii2-widget-datetimepicker";a:3:{s:4:"name";s:35:"kartik-v/yii2-widget-datetimepicker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:16:"@kartik/datetime";s:77:"D:\OSPanel\domains\silverzavod\vendor/kartik-v/yii2-widget-datetimepicker/src";}}s:16:"yiisoft/yii2-jui";a:3:{s:4:"name";s:16:"yiisoft/yii2-jui";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:8:"@yii/jui";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-jui/src";}}s:33:"2amigos/yii2-arrayquery-component";a:3:{s:4:"name";s:33:"2amigos/yii2-arrayquery-component";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:21:"@dosamigos/arrayquery";s:75:"D:\OSPanel\domains\silverzavod\vendor/2amigos/yii2-arrayquery-component/src";}}s:17:"yii2mod/yii2-rbac";a:3:{s:4:"name";s:17:"yii2mod/yii2-rbac";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:13:"@yii2mod/rbac";s:55:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-rbac";}}s:23:"yiisoft/yii2-bootstrap5";a:4:{s:4:"name";s:23:"yiisoft/yii2-bootstrap5";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/bootstrap5";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap5/src";}s:9:"bootstrap";s:40:"yii\bootstrap5\i18n\TranslationBootstrap";}s:18:"yiisoft/yii2-debug";a:3:{s:4:"name";s:18:"yiisoft/yii2-debug";s:7:"version";s:8:"2.1.25.0";s:5:"alias";a:1:{s:10:"@yii/debug";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-debug/src";}}s:18:"yiisoft/yii2-faker";a:3:{s:4:"name";s:18:"yiisoft/yii2-faker";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:10:"@yii/faker";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-faker/src";}}s:16:"yiisoft/yii2-gii";a:3:{s:4:"name";s:16:"yiisoft/yii2-gii";s:7:"version";s:7:"2.2.6.0";s:5:"alias";a:1:{s:8:"@yii/gii";s:58:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-gii/src";}}s:18:"yiisoft/yii2-queue";a:3:{s:4:"name";s:18:"yiisoft/yii2-queue";s:7:"version";s:23:"3.0.9999999.9999999-dev";s:5:"alias";a:10:{s:10:"@yii/queue";s:60:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src";s:13:"@yii/queue/db";s:71:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/db";s:14:"@yii/queue/sqs";s:72:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sqs";s:15:"@yii/queue/file";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/file";s:15:"@yii/queue/sync";s:73:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/sync";s:16:"@yii/queue/redis";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/redis";s:16:"@yii/queue/stomp";s:74:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/stomp";s:18:"@yii/queue/gearman";s:76:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/gearman";s:20:"@yii/queue/beanstalk";s:78:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/beanstalk";s:23:"@yii/queue/amqp_interop";s:81:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-queue/src/drivers/amqp_interop";}}s:26:"yiisoft/yii2-symfonymailer";a:3:{s:4:"name";s:26:"yiisoft/yii2-symfonymailer";s:7:"version";s:7:"2.0.4.0";s:5:"alias";a:1:{s:18:"@yii/symfonymailer";s:68:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-symfonymailer/src";}}s:23:"yiisoft/yii2-bootstrap4";a:3:{s:4:"name";s:23:"yiisoft/yii2-bootstrap4";s:7:"version";s:8:"2.0.11.0";s:5:"alias";a:1:{s:15:"@yii/bootstrap4";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-bootstrap4/src";}}s:20:"yii2mod/yii2-swagger";a:3:{s:4:"name";s:20:"yii2mod/yii2-swagger";s:7:"version";s:7:"1.0.0.0";s:5:"alias";a:1:{s:16:"@yii2mod/swagger";s:58:"D:\OSPanel\domains\silverzavod\vendor/yii2mod/yii2-swagger";}}s:23:"yiisoft/yii2-httpclient";a:3:{s:4:"name";s:23:"yiisoft/yii2-httpclient";s:7:"version";s:10:"dev-master";s:5:"alias";a:1:{s:15:"@yii/httpclient";s:65:"D:\OSPanel\domains\silverzavod\vendor/yiisoft/yii2-httpclient/src";}}}}";s:3:"log";s:35621:"a:1:{s:8:"messages";a:37:{i:0;a:6:{i:0;s:68:"Bootstrap with yii\bootstrap5\i18n\TranslationBootstrap::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.773508;i:4;a:0:{}i:5;i:2611432;}i:1;a:6:{i:0;s:33:"Bootstrap with yii\log\Dispatcher";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.778584;i:4;a:0:{}i:5;i:2728440;}i:2;a:6:{i:0;s:53:"Bootstrap with app\components\SessionTimeoutComponent";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.780402;i:4;a:0:{}i:5;i:2769648;}i:3;a:6:{i:0;s:22:"Bootstrap with Closure";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.780429;i:4;a:0:{}i:5;i:2770024;}i:4;a:6:{i:0;s:21:"Loading module: debug";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.827128;i:4;a:0:{}i:5;i:3915224;}i:5;a:6:{i:0;s:15:"Session started";i:1;i:4;i:2;s:21:"yii\web\Session::open";i:3;d:**********.851316;i:4;a:0:{}i:5;i:4725480;}i:6;a:6:{i:0;s:44:"Bootstrap with yii\debug\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.865713;i:4;a:0:{}i:5;i:5115144;}i:7;a:6:{i:0;s:19:"Loading module: gii";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.878398;i:4;a:0:{}i:5;i:5566352;}i:8;a:6:{i:0;s:42:"Bootstrap with yii\gii\Module::bootstrap()";i:1;i:8;i:2;s:31:"yii\base\Application::bootstrap";i:3;d:**********.879758;i:4;a:0:{}i:5;i:5593744;}i:20;a:6:{i:0;s:64:"Route requested: 'api/manufacter/delete-send-to-material-defect'";i:1;i:8;i:2;s:34:"yii\web\Application::handleRequest";i:3;d:**********.885512;i:4;a:0:{}i:5;i:5766216;}i:21;a:6:{i:0;s:19:"Loading module: api";i:1;i:8;i:2;s:26:"yii\base\Module::getModule";i:3;d:**********.885538;i:4;a:0:{}i:5;i:5767880;}i:22;a:6:{i:0;s:59:"Route to run: api/manufacter/delete-send-to-material-defect";i:1;i:8;i:2;s:30:"yii\base\Controller::runAction";i:3;d:**********.90023;i:4;a:0:{}i:5;i:6128296;}i:23;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:4;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.94083;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7469192;}i:26;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.023054;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8000592;}i:29;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.070355;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8046504;}i:32;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.079201;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8342312;}i:35;a:6:{i:0;s:55:"User '7' logged in from 127.0.0.1. Session not enabled.";i:1;i:4;i:2;s:19:"yii\web\User::login";i:3;d:1748363482.087903;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8628960;}i:36;a:6:{i:0;s:65:"Rate limit skipped: "user" does not implement RateLimitInterface.";i:1;i:4;i:2;s:37:"yii\filters\RateLimiter::beforeAction";i:3;d:1748363482.087962;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8629552;}i:37;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.091266;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8817136;}i:40;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.096393;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8823760;}i:43;a:6:{i:0;s:27:"Checking role: manufacturer";i:1;i:8;i:2;s:40:"yii\rbac\DbManager::checkAccessRecursive";i:3;d:1748363482.100921;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8827152;}i:44;a:6:{i:0;s:100:"Running action: app\modules\api\controllers\ManufacterController::actionDeleteSendToMaterialDefect()";i:1;i:8;i:2;s:36:"yii\base\InlineAction::runWithParams";i:3;d:1748363482.100956;i:4;a:0:{}i:5;i:8826312;}i:45;a:6:{i:0;s:17:"Begin transaction";i:1;i:8;i:2;s:25:"yii\db\Transaction::begin";i:3;d:1748363482.101719;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:128;s:8:"function";s:16:"beginTransaction";s:5:"class";s:17:"yii\db\Connection";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8878936;}i:46;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.102425;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8917104;}i:49;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.110085;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8927784;}i:52;a:6:{i:0;s:121:"SELECT * FROM "material_status_group" WHERE ("id"='42') AND ("deleted_at" IS NULL) AND ("status"=4) AND ("add_user_id"=7)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.113149;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8936160;}i:55;a:6:{i:0;s:102:"SELECT * FROM "tracking" WHERE ("process_id"='42') AND ("progress_type"=20) AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.116467;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8965672;}i:58;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.119208;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8977864;}i:61;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.128575;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8988664;}i:64;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='42') AND ("deleted_at" IS NULL)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.132682;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9018296;}i:67;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.135613;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9036944;}i:70;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.142921;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9047152;}i:73;a:6:{i:0;s:43:"DELETE FROM "material_status" WHERE "id"=19";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.152149;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:172;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9054136;}i:76;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=269";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.156741;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:178;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9187632;}i:79;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:4;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.160491;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9296168;}i:82;a:6:{i:0;s:83:"UPDATE "material_status_group" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=42";i:1;i:4;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.161801;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9295288;}i:85;a:6:{i:0;s:21:"Roll back transaction";i:1;i:8;i:2;s:28:"yii\db\Transaction::rollBack";i:3;d:1748363482.163784;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:206;s:8:"function";s:8:"rollBack";s:5:"class";s:18:"yii\db\Transaction";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9308088;}}}";s:9:"profiling";s:60939:"a:3:{s:6:"memory";i:9471536;s:4:"time";d:0.*****************;s:8:"messages";a:38:{i:24;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:80;i:2;s:23:"yii\db\Connection::open";i:3;d:**********.940879;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7470696;}i:25;a:6:{i:0;s:67:"Opening DB connection: pgsql:host=127.0.0.1;port=5432;dbname=silver";i:1;i:96;i:2;s:23:"yii\db\Connection::open";i:3;d:1748363482.020942;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:7473000;}i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.023118;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8002544;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.06823;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8018280;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.070396;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8048368;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.075061;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8050304;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.079234;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8346576;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.084315;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8349336;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.091295;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8819744;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.095428;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8821912;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.096424;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8826368;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.100123;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8828472;}i:47;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.102464;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8919344;}i:48;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.109499;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8933816;}i:50;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.110118;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8930024;}i:51;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.112809;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8933832;}i:53;a:6:{i:0;s:121:"SELECT * FROM "material_status_group" WHERE ("id"='42') AND ("deleted_at" IS NULL) AND ("status"=4) AND ("add_user_id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.113175;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8939008;}i:54;a:6:{i:0;s:121:"SELECT * FROM "material_status_group" WHERE ("id"='42') AND ("deleted_at" IS NULL) AND ("status"=4) AND ("add_user_id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.115576;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8941928;}i:56;a:6:{i:0;s:102:"SELECT * FROM "tracking" WHERE ("process_id"='42') AND ("progress_type"=20) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.116504;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8969136;}i:57;a:6:{i:0;s:102:"SELECT * FROM "tracking" WHERE ("process_id"='42') AND ("progress_type"=20) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.11885;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8972040;}i:59;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.119276;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8980104;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.127559;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8994656;}i:62;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.128645;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8990904;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.131617;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8993216;}i:65;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='42') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.13272;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9027336;}i:66;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='42') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.13523;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9030504;}i:68;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.135687;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9039184;}i:69;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.142327;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9052192;}i:71;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.14296;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9049392;}i:72;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.150459;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9053192;}i:74;a:6:{i:0;s:43:"DELETE FROM "material_status" WHERE "id"=19";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.152226;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:172;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9057496;}i:75;a:6:{i:0;s:43:"DELETE FROM "material_status" WHERE "id"=19";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.153922;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:172;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9059464;}i:77;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=269";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.156774;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:178;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9190392;}i:78;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=269";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.15801;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:178;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9192400;}i:80;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.160531;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9299544;}i:81;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.161415;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9301528;}i:83;a:6:{i:0;s:83:"UPDATE "material_status_group" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=42";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.16183;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9298064;}i:84;a:6:{i:0;s:83:"UPDATE "material_status_group" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=42";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.162713;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9300072;}}}";s:2:"db";s:59693:"a:1:{s:8:"messages";a:36:{i:27;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.023118;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8002544;}i:28;a:6:{i:0;s:2811:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'users'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.06823;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8018280;}i:30;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.070396;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8048368;}i:31;a:6:{i:0;s:873:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='users'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.075061;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8050304;}i:33;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.079234;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8346576;}i:34;a:6:{i:0;s:85:"SELECT * FROM "users" WHERE "access_token"='681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.084315;i:4;a:2:{i:0;a:5:{s:4:"file";s:59:"D:\OSPanel\domains\silverzavod\modules\api\models\Users.php";s:4:"line";i:39;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:38;s:8:"function";s:12:"beforeAction";s:5:"class";s:18:"yii\web\Controller";s:4:"type";s:2:"->";}}i:5;i:8349336;}i:38;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.091295;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8819744;}i:39;a:6:{i:0;s:51:"SELECT * FROM "auth_assignment" WHERE "user_id"='7'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.095428;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8821912;}i:41;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.096424;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8826368;}i:42;a:6:{i:0;s:53:"SELECT * FROM "auth_item" WHERE "name"='manufacturer'";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.100123;i:4;a:1:{i:0;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:43;s:8:"function";s:3:"can";s:5:"class";s:12:"yii\web\User";s:4:"type";s:2:"->";}}i:5;i:8828472;}i:47;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.102464;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8919344;}i:48;a:6:{i:0;s:2827:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status_group'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.109499;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8933816;}i:50;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.110118;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8930024;}i:51;a:6:{i:0;s:889:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status_group'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.112809;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8933832;}i:53;a:6:{i:0;s:121:"SELECT * FROM "material_status_group" WHERE ("id"='42') AND ("deleted_at" IS NULL) AND ("status"=4) AND ("add_user_id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.113175;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8939008;}i:54;a:6:{i:0;s:121:"SELECT * FROM "material_status_group" WHERE ("id"='42') AND ("deleted_at" IS NULL) AND ("status"=4) AND ("add_user_id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.115576;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:134;s:8:"function";s:7:"findOne";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"::";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8941928;}i:56;a:6:{i:0;s:102:"SELECT * FROM "tracking" WHERE ("process_id"='42') AND ("progress_type"=20) AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.116504;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8969136;}i:57;a:6:{i:0;s:102:"SELECT * FROM "tracking" WHERE ("process_id"='42') AND ("progress_type"=20) AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.11885;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8972040;}i:59;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.119276;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8980104;}i:60;a:6:{i:0;s:2814:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'tracking'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.127559;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8994656;}i:62;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.128645;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8990904;}i:63;a:6:{i:0;s:876:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='tracking'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.131617;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:157;s:8:"function";s:3:"one";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:8993216;}i:65;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='42') AND ("deleted_at" IS NULL)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.13272;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9027336;}i:66;a:6:{i:0;s:89:"SELECT * FROM "material_status" WHERE ("status_group_id"='42') AND ("deleted_at" IS NULL)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.13523;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9030504;}i:68;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.135687;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9039184;}i:69;a:6:{i:0;s:2821:"SELECT
    d.nspname AS table_schema,
    c.relname AS table_name,
    a.attname AS column_name,
    COALESCE(td.typname, tb.typname, t.typname) AS data_type,
    COALESCE(td.typtype, tb.typtype, t.typtype) AS type_type,
    (SELECT nspname FROM pg_namespace WHERE oid = COALESCE(td.typnamespace, tb.typnamespace, t.typnamespace)) AS type_scheme,
    a.attlen AS character_maximum_length,
    pg_catalog.col_description(c.oid, a.attnum) AS column_comment,
    a.atttypmod AS modifier,
    a.attnotnull = false AS is_nullable,
    CAST(pg_get_expr(ad.adbin, ad.adrelid) AS varchar) AS column_default,
    coalesce(pg_get_expr(ad.adbin, ad.adrelid) ~ 'nextval',false) OR attidentity != '' AS is_autoinc,
    pg_get_serial_sequence(quote_ident(d.nspname) || '.' || quote_ident(c.relname), a.attname) AS sequence_name,
    CASE WHEN COALESCE(td.typtype, tb.typtype, t.typtype) = 'e'::char
        THEN array_to_string((SELECT array_agg(enumlabel) FROM pg_enum WHERE enumtypid = COALESCE(td.oid, tb.oid, a.atttypid))::varchar[], ',')
        ELSE NULL
    END AS enum_values,
    CASE atttypid
         WHEN 21 /*int2*/ THEN 16
         WHEN 23 /*int4*/ THEN 32
         WHEN 20 /*int8*/ THEN 64
         WHEN 1700 /*numeric*/ THEN
              CASE WHEN atttypmod = -1
               THEN null
               ELSE ((atttypmod - 4) >> 16) & 65535
               END
         WHEN 700 /*float4*/ THEN 24 /*FLT_MANT_DIG*/
         WHEN 701 /*float8*/ THEN 53 /*DBL_MANT_DIG*/
         ELSE null
      END   AS numeric_precision,
      CASE
        WHEN atttypid IN (21, 23, 20) THEN 0
        WHEN atttypid IN (1700) THEN
        CASE
            WHEN atttypmod = -1 THEN null
            ELSE (atttypmod - 4) & 65535
        END
           ELSE null
      END AS numeric_scale,
    CAST(
             information_schema._pg_char_max_length(information_schema._pg_truetypid(a, t), information_schema._pg_truetypmod(a, t))
             AS numeric
    ) AS size,
    a.attnum = any (ct.conkey) as is_pkey,
    COALESCE(NULLIF(a.attndims, 0), NULLIF(t.typndims, 0), (t.typcategory='A')::int) AS dimension
FROM
    pg_class c
    LEFT JOIN pg_attribute a ON a.attrelid = c.oid
    LEFT JOIN pg_attrdef ad ON a.attrelid = ad.adrelid AND a.attnum = ad.adnum
    LEFT JOIN pg_type t ON a.atttypid = t.oid
    LEFT JOIN pg_type tb ON (a.attndims > 0 OR t.typcategory='A') AND t.typelem > 0 AND t.typelem = tb.oid OR t.typbasetype > 0 AND t.typbasetype = tb.oid
    LEFT JOIN pg_type td ON t.typndims > 0 AND t.typbasetype > 0 AND tb.typelem = td.oid
    LEFT JOIN pg_namespace d ON d.oid = c.relnamespace
    LEFT JOIN pg_constraint ct ON ct.conrelid = c.oid AND ct.contype = 'p'
WHERE
    a.attnum > 0 AND t.typname != '' AND NOT a.attisdropped
    AND c.relname = 'material_status'
    AND d.nspname = 'public'
ORDER BY
    a.attnum;";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.142327;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9052192;}i:71;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.14296;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9049392;}i:72;a:6:{i:0;s:883:"select
    ct.conname as constraint_name,
    a.attname as column_name,
    fc.relname as foreign_table_name,
    fns.nspname as foreign_table_schema,
    fa.attname as foreign_column_name
from
    (SELECT ct.conname, ct.conrelid, ct.confrelid, ct.conkey, ct.contype, ct.confkey, generate_subscripts(ct.conkey, 1) AS s
       FROM pg_constraint ct
    ) AS ct
    inner join pg_class c on c.oid=ct.conrelid
    inner join pg_namespace ns on c.relnamespace=ns.oid
    inner join pg_attribute a on a.attrelid=ct.conrelid and a.attnum = ct.conkey[ct.s]
    left join pg_class fc on fc.oid=ct.confrelid
    left join pg_namespace fns on fc.relnamespace=fns.oid
    left join pg_attribute fa on fa.attrelid=ct.confrelid and fa.attnum = ct.confkey[ct.s]
where
    ct.contype='f'
    and c.relname='material_status'
    and ns.nspname='public'
order by
    fns.nspname, fc.relname, a.attnum";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.150459;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:169;s:8:"function";s:3:"all";s:5:"class";s:18:"yii\db\ActiveQuery";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9053192;}i:74;a:6:{i:0;s:43:"DELETE FROM "material_status" WHERE "id"=19";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.152226;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:172;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9057496;}i:75;a:6:{i:0;s:43:"DELETE FROM "material_status" WHERE "id"=19";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.153922;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:172;s:8:"function";s:6:"delete";s:5:"class";s:19:"yii\db\ActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9059464;}i:77;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=269";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.156774;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:178;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9190392;}i:78;a:6:{i:0;s:71:"UPDATE "tracking" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=269";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.15801;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:178;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9192400;}i:80;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:80;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.160531;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9299544;}i:81;a:6:{i:0;s:57:"SELECT EXISTS(SELECT * FROM "users" WHERE "users"."id"=7)";i:1;i:96;i:2;s:21:"yii\db\Command::query";i:3;d:1748363482.161415;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9301528;}i:83;a:6:{i:0;s:83:"UPDATE "material_status_group" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=42";i:1;i:80;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.16183;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9298064;}i:84;a:6:{i:0;s:83:"UPDATE "material_status_group" SET "deleted_at"='2025-05-27 21:31:22' WHERE "id"=42";i:1;i:96;i:2;s:23:"yii\db\Command::execute";i:3;d:1748363482.162713;i:4;a:3:{i:0;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:185;s:8:"function";s:4:"save";s:5:"class";s:23:"yii\db\BaseActiveRecord";s:4:"type";s:2:"->";}i:1;a:5:{s:4:"file";s:100:"D:\OSPanel\domains\silverzavod\modules\api\services\manufacter\DeleteSendToMaterialDefectService.php";s:4:"line";i:38;s:8:"function";s:26:"deleteMaterialReturnRecord";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}i:2;a:5:{s:4:"file";s:79:"D:\OSPanel\domains\silverzavod\modules\api\controllers\ManufacterController.php";s:4:"line";i:686;s:8:"function";s:20:"deleteMaterialDefect";s:5:"class";s:69:"app\modules\api\services\manufacter\DeleteSendToMaterialDefectService";s:4:"type";s:2:"->";}}i:5;i:9300072;}}}";s:5:"event";s:8705:"a:47:{i:0;a:5:{s:4:"time";d:**********.884094;s:4:"name";s:13:"beforeRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:1;a:5:{s:4:"time";d:**********.900963;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:2;a:5:{s:4:"time";d:**********.900998;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:3;a:5:{s:4:"time";d:**********.930889;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:4;a:5:{s:4:"time";d:1748363482.020914;s:4:"name";s:9:"afterOpen";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:5;a:5:{s:4:"time";d:1748363482.084623;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:6;a:5:{s:4:"time";d:1748363482.084674;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:28:"app\modules\api\models\Users";}i:7;a:5:{s:4:"time";d:1748363482.084958;s:4:"name";s:11:"beforeLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:8;a:5:{s:4:"time";d:1748363482.087713;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:9;a:5:{s:4:"time";d:1748363482.08775;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:10;a:5:{s:4:"time";d:1748363482.087762;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:11;a:5:{s:4:"time";d:1748363482.087771;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:12;a:5:{s:4:"time";d:1748363482.087778;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:13;a:5:{s:4:"time";d:1748363482.087786;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:14;a:5:{s:4:"time";d:1748363482.087794;s:4:"name";s:18:"missingTranslation";s:5:"class";s:32:"yii\i18n\MissingTranslationEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"yii\i18n\PhpMessageSource";}i:15;a:5:{s:4:"time";d:1748363482.087935;s:4:"name";s:10:"afterLogin";s:5:"class";s:17:"yii\web\UserEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:12:"yii\web\User";}i:16;a:5:{s:4:"time";d:1748363482.088001;s:4:"name";s:12:"beforeAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:17;a:5:{s:4:"time";d:1748363482.101747;s:4:"name";s:16:"beginTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:18;a:5:{s:4:"time";d:1748363482.102341;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:19;a:5:{s:4:"time";d:1748363482.115872;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:20;a:5:{s:4:"time";d:1748363482.115914;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:21;a:5:{s:4:"time";d:1748363482.116347;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:22;a:5:{s:4:"time";d:1748363482.119079;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:23;a:5:{s:4:"time";d:1748363482.131992;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:24;a:5:{s:4:"time";d:1748363482.132544;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:25;a:5:{s:4:"time";d:1748363482.135478;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:26;a:5:{s:4:"time";d:1748363482.151318;s:4:"name";s:9:"afterFind";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:27;a:5:{s:4:"time";d:1748363482.151969;s:4:"name";s:12:"beforeDelete";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:28;a:5:{s:4:"time";d:1748363482.15424;s:4:"name";s:11:"afterDelete";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:32:"app\common\models\MaterialStatus";}i:29;a:5:{s:4:"time";d:1748363482.154321;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:30;a:5:{s:4:"time";d:1748363482.156592;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:31;a:5:{s:4:"time";d:1748363482.15661;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:32;a:5:{s:4:"time";d:1748363482.158426;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:26:"app\common\models\Tracking";}i:33;a:5:{s:4:"time";d:1748363482.158487;s:4:"name";s:14:"beforeValidate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:34;a:5:{s:4:"time";d:1748363482.159773;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:35;a:5:{s:4:"time";d:1748363482.159885;s:4:"name";s:4:"init";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:18:"yii\db\ActiveQuery";}i:36;a:5:{s:4:"time";d:1748363482.161665;s:4:"name";s:13:"afterValidate";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:37;a:5:{s:4:"time";d:1748363482.161686;s:4:"name";s:12:"beforeUpdate";s:5:"class";s:19:"yii\base\ModelEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:38;a:5:{s:4:"time";d:1748363482.162876;s:4:"name";s:11:"afterUpdate";s:5:"class";s:21:"yii\db\AfterSaveEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:37:"app\common\models\MaterialStatusGroup";}i:39;a:5:{s:4:"time";d:1748363482.164077;s:4:"name";s:19:"rollbackTransaction";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:17:"yii\db\Connection";}i:40;a:5:{s:4:"time";d:1748363482.164652;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:48:"app\modules\api\controllers\ManufacterController";}i:41;a:5:{s:4:"time";d:1748363482.165792;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:25:"app\modules\api\ApiModule";}i:42;a:5:{s:4:"time";d:1748363482.165819;s:4:"name";s:11:"afterAction";s:5:"class";s:20:"yii\base\ActionEvent";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:43;a:5:{s:4:"time";d:1748363482.165846;s:4:"name";s:12:"afterRequest";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:19:"yii\web\Application";}i:44;a:5:{s:4:"time";d:1748363482.165866;s:4:"name";s:10:"beforeSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:45;a:5:{s:4:"time";d:1748363482.170936;s:4:"name";s:12:"afterPrepare";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}i:46;a:5:{s:4:"time";d:1748363482.171167;s:4:"name";s:9:"afterSend";s:5:"class";s:14:"yii\base\Event";s:8:"isStatic";s:1:"0";s:11:"senderClass";s:16:"yii\web\Response";}}";s:4:"mail";s:6:"a:0:{}";s:8:"timeline";s:91:"a:3:{s:5:"start";d:**********.738697;s:3:"end";d:1748363482.177739;s:6:"memory";i:9503464;}";s:4:"dump";s:6:"a:0:{}";s:6:"router";s:2259:"a:3:{s:8:"messages";a:11:{i:9;a:6:{i:0;a:3:{s:4:"rule";s:3:"gii";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885278;i:4;a:0:{}i:5;i:5758304;}i:10;a:6:{i:0;a:3:{s:4:"rule";s:12:"gii/<id:\w+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885321;i:4;a:0:{}i:5;i:5759056;}i:11;a:6:{i:0;a:3:{s:4:"rule";s:41:"gii/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885339;i:4;a:0:{}i:5;i:5759808;}i:12;a:6:{i:0;a:3:{s:4:"rule";s:5:"debug";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885356;i:4;a:0:{}i:5;i:5760560;}i:13;a:6:{i:0;a:3:{s:4:"rule";s:43:"debug/<controller:[\w\-]+>/<action:[\w\-]+>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885371;i:4;a:0:{}i:5;i:5761312;}i:14;a:6:{i:0;a:3:{s:4:"rule";s:1:"/";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885386;i:4;a:0:{}i:5;i:5762064;}i:15;a:6:{i:0;a:3:{s:4:"rule";s:5:"login";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885402;i:4;a:0:{}i:5;i:5762816;}i:16;a:6:{i:0;a:3:{s:4:"rule";s:6:"logout";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885417;i:4;a:0:{}i:5;i:5763568;}i:17;a:6:{i:0;a:3:{s:4:"rule";s:13:"site/<action>";s:5:"match";b:0;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885436;i:4;a:0:{}i:5;i:5764960;}i:18;a:6:{i:0;s:69:"Request parsed with URL rule: api/<controller:[\w-]+>/<action:[\w-]+>";i:1;i:8;i:2;s:29:"yii\web\UrlRule::parseRequest";i:3;d:**********.885478;i:4;a:0:{}i:5;i:5767080;}i:19;a:6:{i:0;a:3:{s:4:"rule";s:39:"api/<controller:[\w-]+>/<action:[\w-]+>";s:5:"match";b:1;s:6:"parent";N;}i:1;i:8;i:2;s:32:"yii\web\UrlManager::parseRequest";i:3;d:**********.885494;i:4;a:0:{}i:5;i:5766872;}}s:5:"route";s:45:"api/manufacter/delete-send-to-material-defect";s:6:"action";s:84:"app\modules\api\controllers\ManufacterController::actionDeleteSendToMaterialDefect()";}";s:7:"request";s:4443:"a:15:{s:7:"flashes";a:0:{}s:10:"statusCode";i:500;s:14:"requestHeaders";a:11:{s:13:"authorization";s:47:"Bearer 681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy";s:10:"user-agent";s:21:"PostmanRuntime/7.44.0";s:6:"accept";s:3:"*/*";s:13:"cache-control";s:8:"no-cache";s:13:"postman-token";s:36:"2514d7fc-204c-44f9-bcbe-da8c5d2f18b1";s:4:"host";s:6:"silver";s:15:"accept-encoding";s:17:"gzip, deflate, br";s:10:"connection";s:10:"keep-alive";s:12:"content-type";s:80:"multipart/form-data; boundary=--------------------------375329532119579504052942";s:6:"cookie";s:216:"PHPSESSID=3d53e1a8gfu7bp6plf1p24uk2t09vd8d; _csrf=14ec51ee7e0926b662e6e4c28aae809f96f4dbcd32b828cfe65728de40638f7da%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22Nqbj5GqxpiP8D61KTcKqr-5XTAoPs5qg%22%3B%7D";s:14:"content-length";s:3:"274";}s:15:"responseHeaders";a:9:{s:7:"Expires";s:29:"Thu, 19 Nov 1981 08:52:00 GMT";s:13:"Cache-Control";s:35:"no-store, no-cache, must-revalidate";s:6:"Pragma";s:8:"no-cache";s:4:"Vary";s:6:"Accept";s:12:"Content-Type";s:31:"application/json; charset=UTF-8";s:11:"X-Debug-Tag";s:13:"6835e8d9d3bb5";s:16:"X-Debug-Duration";s:3:"433";s:12:"X-Debug-Link";s:37:"/debug/default/view?tag=6835e8d9d3bb5";s:10:"Set-Cookie";s:204:"_csrf=adebfba1e93cfb89e02028b8b135e88348b6e6ed0a40e25c3afd615be0e2ab2fa%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%229qjWwK9Ltmm2ECMTKgR4JrLuTZONZ5X9%22%3B%7D; path=/; HttpOnly; SameSite=Lax";}s:5:"route";s:45:"api/manufacter/delete-send-to-material-defect";s:6:"action";s:84:"app\modules\api\controllers\ManufacterController::actionDeleteSendToMaterialDefect()";s:12:"actionParams";a:0:{}s:7:"general";a:5:{s:6:"method";s:4:"POST";s:6:"isAjax";b:0;s:6:"isPjax";b:0;s:7:"isFlash";b:0;s:18:"isSecureConnection";b:0;}s:11:"requestBody";a:0:{}s:6:"SERVER";a:39:{s:15:"REDIRECT_STATUS";s:3:"200";s:15:"HTTP_USER_AGENT";s:21:"PostmanRuntime/7.44.0";s:11:"HTTP_ACCEPT";s:3:"*/*";s:18:"HTTP_CACHE_CONTROL";s:8:"no-cache";s:18:"HTTP_POSTMAN_TOKEN";s:36:"2514d7fc-204c-44f9-bcbe-da8c5d2f18b1";s:9:"HTTP_HOST";s:6:"silver";s:20:"HTTP_ACCEPT_ENCODING";s:17:"gzip, deflate, br";s:15:"HTTP_CONNECTION";s:10:"keep-alive";s:12:"CONTENT_TYPE";s:80:"multipart/form-data; boundary=--------------------------375329532119579504052942";s:11:"HTTP_COOKIE";s:216:"PHPSESSID=3d53e1a8gfu7bp6plf1p24uk2t09vd8d; _csrf=14ec51ee7e0926b662e6e4c28aae809f96f4dbcd32b828cfe65728de40638f7da%3A2%3A%7Bi%3A0%3Bs%3A5%3A%22_csrf%22%3Bi%3A1%3Bs%3A32%3A%22Nqbj5GqxpiP8D61KTcKqr-5XTAoPs5qg%22%3B%7D";s:14:"CONTENT_LENGTH";s:3:"274";s:4:"PATH";s:343:"d:\ospanel\modules\php\PHP_8.1\ext;d:\ospanel\modules\php\PHP_8.1\pear;d:\ospanel\modules\php\PHP_8.1\pear\bin;d:\ospanel\modules\php\PHP_8.1;d:\ospanel\modules\wget\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1\bin;d:\ospanel\modules\http\Apache_2.4-PHP_8.0-8.1;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\system32\Wbem;C:\WINDOWS\SysWOW64";s:10:"SystemRoot";s:10:"C:\WINDOWS";s:7:"COMSPEC";s:27:"C:\WINDOWS\system32\cmd.exe";s:7:"PATHEXT";s:62:".COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC;.PY;.PYW";s:6:"WINDIR";s:10:"C:\WINDOWS";s:16:"SERVER_SIGNATURE";s:0:"";s:15:"SERVER_SOFTWARE";s:6:"Apache";s:11:"SERVER_NAME";s:6:"silver";s:11:"SERVER_ADDR";s:9:"127.0.0.1";s:11:"SERVER_PORT";s:2:"80";s:11:"REMOTE_ADDR";s:9:"127.0.0.1";s:13:"DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:14:"REQUEST_SCHEME";s:4:"http";s:14:"CONTEXT_PREFIX";s:0:"";s:21:"CONTEXT_DOCUMENT_ROOT";s:34:"D:/OSPanel/domains/silverzavod/web";s:12:"SERVER_ADMIN";s:18:"[no address given]";s:15:"SCRIPT_FILENAME";s:44:"D:/OSPanel/domains/silverzavod/web/index.php";s:11:"REMOTE_PORT";s:5:"54617";s:12:"REDIRECT_URL";s:46:"/api/manufacter/delete-send-to-material-defect";s:17:"GATEWAY_INTERFACE";s:7:"CGI/1.1";s:15:"SERVER_PROTOCOL";s:8:"HTTP/1.1";s:14:"REQUEST_METHOD";s:4:"POST";s:12:"QUERY_STRING";s:0:"";s:11:"REQUEST_URI";s:46:"/api/manufacter/delete-send-to-material-defect";s:11:"SCRIPT_NAME";s:10:"/index.php";s:8:"PHP_SELF";s:10:"/index.php";s:18:"REQUEST_TIME_FLOAT";d:**********.704041;s:12:"REQUEST_TIME";i:**********;}s:3:"GET";a:0:{}s:4:"POST";a:2:{s:8:"group_id";s:2:"42";s:4:"type";s:6:"return";}s:6:"COOKIE";a:2:{s:9:"PHPSESSID";s:32:"3d53e1a8gfu7bp6plf1p24uk2t09vd8d";s:5:"_csrf";s:130:"14ec51ee7e0926b662e6e4c28aae809f96f4dbcd32b828cfe65728de40638f7da:2:{i:0;s:5:"_csrf";i:1;s:32:"Nqbj5GqxpiP8D61KTcKqr-5XTAoPs5qg";}";}s:5:"FILES";a:0:{}s:7:"SESSION";a:1:{s:7:"__flash";a:0:{}}}";s:4:"user";s:2147:"a:5:{s:2:"id";i:7;s:8:"identity";a:8:{s:2:"id";s:1:"7";s:8:"username";s:14:"'manufacturer'";s:9:"full_name";s:20:"'Ishlab chiqaruvchi'";s:4:"role";s:1:"0";s:12:"access_token";s:42:"'681RqyxaZoKE74gNR87UQnKBAUVhzx3swPze8DHy'";s:8:"password";s:62:"'$2y$13$8ymQqJl5qbjAvbz9d5fbFuOFZGcDCeN6PAzjPCDDcPlYo7inyMDbG'";s:10:"created_at";s:21:"'2025-03-17 10:58:05'";s:10:"deleted_at";s:4:"null";}s:10:"attributes";a:8:{i:0;a:2:{s:9:"attribute";s:2:"id";s:5:"label";s:2:"Id";}i:1;a:2:{s:9:"attribute";s:8:"username";s:5:"label";s:10:"Логин";}i:2;a:2:{s:9:"attribute";s:9:"full_name";s:5:"label";s:9:"Full Name";}i:3;a:2:{s:9:"attribute";s:4:"role";s:5:"label";s:4:"Role";}i:4;a:2:{s:9:"attribute";s:12:"access_token";s:5:"label";s:12:"Access Token";}i:5;a:2:{s:9:"attribute";s:8:"password";s:5:"label";s:10:"Парол";}i:6;a:2:{s:9:"attribute";s:10:"created_at";s:5:"label";s:10:"Created At";}i:7;a:2:{s:9:"attribute";s:10:"deleted_at";s:5:"label";s:10:"Deleted At";}}s:13:"rolesProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";N;s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:1:{s:12:"manufacturer";a:7:{s:4:"type";i:1;s:4:"name";s:12:"manufacturer";s:11:"description";s:12:"Manufacturer";s:8:"ruleName";N;s:4:"data";s:4:"null";s:9:"createdAt";i:**********;s:9:"updatedAt";i:**********;}}s:10:"modelClass";N;}s:19:"permissionsProvider";O:26:"yii\data\ArrayDataProvider":12:{s:27:" yii\base\Component _events";a:0:{}s:35:" yii\base\Component _eventWildcards";a:0:{}s:30:" yii\base\Component _behaviors";N;s:2:"id";s:4:"dp-1";s:32:" yii\data\BaseDataProvider _sort";N;s:38:" yii\data\BaseDataProvider _pagination";N;s:32:" yii\data\BaseDataProvider _keys";N;s:34:" yii\data\BaseDataProvider _models";N;s:38:" yii\data\BaseDataProvider _totalCount";N;s:3:"key";N;s:9:"allModels";a:0:{}s:10:"modelClass";N;}}";s:5:"asset";s:6:"a:0:{}";s:7:"summary";a:13:{s:3:"tag";s:13:"6835e8d9d3bb5";s:3:"url";s:59:"http://silver/api/manufacter/delete-send-to-material-defect";s:4:"ajax";i:0;s:6:"method";s:4:"POST";s:2:"ip";s:9:"127.0.0.1";s:4:"time";d:**********.704041;s:10:"statusCode";i:500;s:8:"sqlCount";i:18;s:21:"excessiveCallersCount";i:0;s:9:"mailCount";i:0;s:9:"mailFiles";a:0:{}s:10:"peakMemory";i:9471536;s:14:"processingTime";d:0.*****************;}s:10:"exceptions";a:0:{}}